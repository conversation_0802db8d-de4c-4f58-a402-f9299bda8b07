/* CSS cho các tính năng mở rộng trong Admin Dashboard */

/* === Trang Hỗ trợ khách hàng === */
.support-list-container {
  margin-top: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.support-status {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
}

.support-status.new {
  background-color: #e3f2fd;
  color: #0d47a1;
}

.support-status.processing {
  background-color: #fff3e0;
  color: #e65100;
}

.support-status.resolved {
  background-color: #e8f5e9;
  color: #1b5e20;
}

.support-action-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  margin: 0 3px;
  color: #555;
  transition: color 0.2s;
}

.support-action-btn:hover {
  color: #4e73df;
}

.support-action-btn.view {
  color: #4e73df;
}

.support-action-btn.reply {
  color: #1cc88a;
}

.support-action-btn.resolve {
  color: #36b9cc;
}

.support-subject {
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* === Trang Báo cáo === */
.reports-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-top: 20px;
  margin-bottom: 30px;
}

@media (max-width: 992px) {
  .reports-grid {
    grid-template-columns: 1fr;
  }
}

.report-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  align-items: flex-start;
  transition: transform 0.2s, box-shadow 0.2s;
}

.report-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.report-icon {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  background-color: #f0f4ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 20px;
  color: #4e73df;
}

#sales-report-card .report-icon {
  background-color: #f0f4ff;
  color: #4e73df;
}

#inventory-report-card .report-icon {
  background-color: #edfcf5;
  color: #1cc88a;
}

#customer-report-card .report-icon {
  background-color: #fff9eb;
  color: #f6c23e;
}

#custom-report-card .report-icon {
  background-color: #f5f5f5;
  color: #666;
}

.report-content {
  flex-grow: 1;
}

.report-content h3 {
  margin: 0 0 8px;
  font-size: 16px;
  color: #333;
}

.report-content p {
  margin: 0 0 15px;
  color: #666;
  font-size: 14px;
}

.report-actions {
  margin-top: 15px;
}

.generate-report {
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.recent-reports {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 30px;
}

.recent-reports h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  color: #333;
}

.report-type {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: white;
  background-color: #4e73df;
}

.report-type.sales {
  background-color: #4e73df;
}

.report-type.inventory {
  background-color: #1cc88a;
}

.report-type.customers {
  background-color: #f6c23e;
}

.report-type.custom {
  background-color: #666;
}

.report-action-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  margin: 0 3px;
  color: #555;
  transition: color 0.2s;
}

.report-action-btn:hover {
  color: #4e73df;
}

.report-action-btn.view {
  color: #4e73df;
}

.report-action-btn.download {
  color: #1cc88a;
}

.report-action-btn.delete {
  color: #e74a3b;
}

/* Modal tạo báo cáo tùy chỉnh */
.custom-report-modal {
  max-width: 700px !important;
}

.report-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

@media (max-width: 768px) {
  .report-options {
    grid-template-columns: 1fr;
  }
}

.report-option-group {
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 15px;
}

.report-option-group h4 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 15px;
  color: #333;
}

.report-date-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.report-checkbox-option {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 5px;
}

.report-checkbox-option input[type="checkbox"] {
  margin: 0;
}

/* Cải thiện thiết kế chung */
.section-header {
  margin-bottom: 20px;
}

.btn {
  padding: 8px 12px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s;
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  border: none;
}

.btn-primary {
  background-color: #4e73df;
  color: white;
}

.btn-primary:hover {
  background-color: #2e59d9;
}

.btn-secondary {
  background-color: #f8f9fc;
  color: #5a5c69;
  border: 1px solid #eaecf4;
}

.btn-secondary:hover {
  background-color: #eaecf4;
}

.btn-success {
  background-color: #1cc88a;
  color: white;
}

.btn-success:hover {
  background-color: #17a673;
}

.btn-danger {
  background-color: #e74a3b;
  color: white;
}

.btn-danger:hover {
  background-color: #d52a1a;
}

/* Loading animation */
.loading i {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}