/* CSS cho modal đơn hàng và các thành phần liên quan */

/* Modal đơn hàng */
.orders-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.orders-content {
    background-color: #fff;
    border-radius: 8px;
    width: 90%;
    max-width: 1000px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    animation: slideIn 0.3s ease-out;
}

.orders-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
    background-color: #f8f9fa;
}

.orders-header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: #333;
}

.orders-header .close-btn {
    font-size: 24px;
    cursor: pointer;
    color: #666;
    transition: color 0.2s;
}

.orders-header .close-btn:hover {
    color: #ff5722;
}

.orders-filters {
    padding: 15px 20px;
    display: flex;
    gap: 15px;
    border-bottom: 1px solid #eee;
    background-color: #f8f9fa;
}

.orders-filters select,
.orders-filters input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.orders-filters select {
    min-width: 180px;
}

.orders-filters input {
    flex-grow: 1;
}

.orders-list {
    flex-grow: 1;
    overflow-y: auto;
    padding: 20px;
}

#orders-loading,
#orders-empty {
    text-align: center;
    padding: 20px;
    color: #666;
    font-style: italic;
}

/* Card đơn hàng */
.order-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 15px;
    padding: 15px;
    border-left: 4px solid #ddd;
}

.order-card[data-order-status="processing"] {
    border-left-color: #ff9800;
}

.order-card[data-order-status="confirmed"] {
    border-left-color: #2196F3;
}

.order-card[data-order-status="shipping"] {
    border-left-color: #9c27b0;
}

.order-card[data-order-status="delivered"] {
    border-left-color: #4CAF50;
}

.order-card[data-order-status="cancelled"] {
    border-left-color: #f44336;
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 10px;
}

.order-id .label,
.order-date .label {
    font-weight: normal;
    color: #666;
    margin-right: 5px;
}

.order-id .value {
    font-weight: bold;
    color: #333;
}

.order-date .value {
    color: #333;
}

.order-status {
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-processing {
    background-color: #fff3e0;
    color: #e65100;
}

.status-confirmed {
    background-color: #e3f2fd;
    color: #0d47a1;
}

.status-shipping {
    background-color: #f3e5f5;
    color: #6a1b9a;
}

.status-delivered {
    background-color: #e8f5e9;
    color: #1b5e20;
}

.status-cancelled {
    background-color: #ffebee;
    color: #b71c1c;
}

.order-summary {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px dashed #eee;
}

.order-products .label,
.order-total .label {
    color: #666;
    margin-right: 5px;
}

.order-total .value {
    font-weight: bold;
    color: #ff5722;
}

.order-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.btn-view-detail,
.btn-cancel-order {
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 13px;
    cursor: pointer;
    border: none;
    transition: background-color 0.2s, color 0.2s;
}

.btn-view-detail {
    background-color: #2196F3;
    color: white;
}

.btn-cancel-order {
    background-color: #f44336;
    color: white;
}

.btn-view-detail:hover {
    background-color: #1976D2;
}

.btn-cancel-order:hover {
    background-color: #D32F2F;
}

/* Modal chi tiết đơn hàng */
.order-detail-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1001;
    justify-content: center;
    align-items: center;
}

.detail-content {
    background-color: #fff;
    border-radius: 8px;
    width: 90%;
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    animation: slideIn 0.3s ease-out;
}

.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
    position: sticky;
    top: 0;
    background-color: #fff;
    z-index: 10;
}

.detail-header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: #333;
}

.detail-section {
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.detail-section h3 {
    margin-top: 0;
    color: #333;
    font-size: 1.2rem;
}

.detail-row {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
}

.detail-column {
    flex: 1;
    min-width: 250px;
}

.detail-column p {
    margin: 8px 0;
    color: #333;
}

.order-status-large {
    display: inline-block;
    padding: 8px 16px;
    border-radius: 20px;
    margin-bottom: 15px;
}

.product-list {
    border: 1px solid #eee;
    border-radius: 4px;
    overflow: hidden;
}

.order-product-item {
    display: flex;
    padding: 15px;
    border-bottom: 1px solid #eee;
}

.order-product-item:last-child {
    border-bottom: none;
}

.product-image {
    width: 80px;
    height: 80px;
    margin-right: 15px;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
}

.product-details {
    flex-grow: 1;
}

.product-name {
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.product-quantity,
.product-price {
    color: #666;
    font-size: 14px;
    margin-bottom: 3px;
}

.product-total {
    font-weight: bold;
    color: #333;
    display: flex;
    align-items: center;
}

.summary-table {
    width: 100%;
    max-width: 400px;
    margin-left: auto;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
}

.summary-row.total {
    border-top: 1px solid #eee;
    padding-top: 15px;
    margin-top: 5px;
}

.summary-label {
    color: #666;
}

.summary-value {
    font-weight: bold;
    color: #333;
}

.summary-row.total .summary-value {
    color: #ff5722;
    font-size: 1.2em;
}

.detail-actions {
    padding: 20px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.btn-close-detail,
.btn-cancel-order-detail {
    padding: 10px 20px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    border: none;
    transition: background-color 0.2s, color 0.2s;
}

.btn-close-detail {
    background-color: #e0e0e0;
    color: #333;
}

.btn-cancel-order-detail {
    background-color: #f44336;
    color: white;
}

.btn-close-detail:hover {
    background-color: #bdbdbd;
}

.btn-cancel-order-detail:hover {
    background-color: #d32f2f;
}

/* Animations */
@keyframes slideIn {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Responsive styles */
@media (max-width: 768px) {
    .orders-filters {
        flex-direction: column;
    }
    
    .order-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .order-summary {
        flex-direction: column;
        gap: 10px;
    }
    
    .detail-row {
        flex-direction: column;
    }
    
    .detail-actions {
        flex-direction: column;
    }
    
    .btn-close-detail,
    .btn-cancel-order-detail {
        width: 100%;
    }
}