// <PERSON><PERSON> thống giỏ hàng thống nhất cho NA Food
class CartManager {
  constructor() {
    this.cart = this.loadCart();
    this.init();
  }

  // Khởi tạo sự kiện
  init() {
    this.updateCartDisplay();
    this.setupEventListeners();
  }

  // Thiết lập sự kiện
  setupEventListeners() {
    // Nút mở giỏ hàng
    const cartBtn = document.querySelector('.cart-btn');
    if (cartBtn) {
      cartBtn.addEventListener('click', () => this.openCart());
    }

    // Nút đóng giỏ hàng
    const closeBtns = document.querySelectorAll('.cart-close-btn, .close-btn');
    closeBtns.forEach(btn => {
      btn.addEventListener('click', () => this.closeCart());
    });

    // Overlay
    const overlay = document.querySelector('.cart-overlay');
    if (overlay) {
      overlay.addEventListener('click', () => this.closeCart());
    }
  }

  // Load giỏ hàng từ localStorage
  loadCart() {
    try {
      return JSON.parse(localStorage.getItem('nafood_cart')) || [];
    } catch (error) {
      console.error('Lỗi khi load giỏ hàng:', error);
      return [];
    }
  }

  // Lưu giỏ hàng vào localStorage
  saveCart() {
    try {
      localStorage.setItem('nafood_cart', JSON.stringify(this.cart));
    } catch (error) {
      console.error('Lỗi khi lưu giỏ hàng:', error);
    }
  }

  // Thêm sản phẩm vào giỏ hàng
  addToCart(product) {
    const { id, title, price, img, quantity = 1, note = '' } = product;
    
    // Kiểm tra sản phẩm đã có trong giỏ hàng chưa
    const existingItem = this.cart.find(item => item.id === id);
    
    if (existingItem) {
      existingItem.quantity += quantity;
      if (note) existingItem.note = note;
    } else {
      this.cart.push({
        id,
        title,
        price,
        img,
        quantity,
        note
      });
    }
    
    this.saveCart();
    this.updateCartDisplay();
    this.showNotification('Đã thêm sản phẩm vào giỏ hàng!');
  }

  // Xóa sản phẩm khỏi giỏ hàng
  removeFromCart(productId) {
    this.cart = this.cart.filter(item => item.id !== productId);
    this.saveCart();
    this.updateCartDisplay();
  }

  // Cập nhật số lượng sản phẩm
  updateQuantity(productId, newQuantity) {
    const item = this.cart.find(item => item.id === productId);
    if (item) {
      if (newQuantity <= 0) {
        this.removeFromCart(productId);
      } else {
        item.quantity = newQuantity;
        this.saveCart();
        this.updateCartDisplay();
      }
    }
  }

  // Lấy tổng số lượng sản phẩm
  getTotalItems() {
    return this.cart.reduce((total, item) => total + item.quantity, 0);
  }

  // Lấy tổng tiền
  getTotalPrice() {
    return this.cart.reduce((total, item) => total + (item.price * item.quantity), 0);
  }

  // Cập nhật hiển thị giỏ hàng
  updateCartDisplay() {
    this.updateCartCount();
    this.updateCartItems();
    this.updateCartTotal();
  }

  // Cập nhật số lượng trên icon giỏ hàng
  updateCartCount() {
    const totalItems = this.getTotalItems();
    const cartCountElements = document.querySelectorAll('.cart-count, #cart-count');
    
    cartCountElements.forEach(element => {
      element.textContent = totalItems;
      element.style.display = totalItems > 0 ? 'inline' : 'none';
    });
  }

  // Cập nhật danh sách sản phẩm trong giỏ hàng
  updateCartItems() {
    const cartItemsContainer = document.querySelector('.cart-items');
    if (!cartItemsContainer) return;

    if (this.cart.length === 0) {
      cartItemsContainer.innerHTML = '<p class="empty-cart">Giỏ hàng trống</p>';
      return;
    }

    cartItemsContainer.innerHTML = this.cart.map(item => `
      <div class="cart-item" data-id="${item.id}">
        <img src="${item.img}" alt="${item.title}" class="cart-item-image">
        <div class="cart-item-info">
          <h4 class="cart-item-name">${item.title}</h4>
          <p class="cart-item-price">${item.price.toLocaleString()}₫</p>
          ${item.note ? `<p class="cart-item-note">Ghi chú: ${item.note}</p>` : ''}
        </div>
        <div class="cart-item-controls">
          <button class="quantity-btn" onclick="cartManager.updateQuantity('${item.id}', ${item.quantity - 1})">-</button>
          <span class="quantity">${item.quantity}</span>
          <button class="quantity-btn" onclick="cartManager.updateQuantity('${item.id}', ${item.quantity + 1})">+</button>
        </div>
        <button class="remove-btn" onclick="cartManager.removeFromCart('${item.id}')">🗑️</button>
      </div>
    `).join('');
  }

  // Cập nhật tổng tiền
  updateCartTotal() {
    const totalPrice = this.getTotalPrice();
    const totalElements = document.querySelectorAll('.cart-total, .total-price');
    
    totalElements.forEach(element => {
      element.textContent = totalPrice.toLocaleString() + '₫';
    });
  }

  // Mở giỏ hàng
  openCart() {
    const cartContainer = document.querySelector('.cart-container');
    const overlay = document.querySelector('.cart-overlay');
    
    if (cartContainer) cartContainer.classList.add('active');
    if (overlay) overlay.style.display = 'block';
    document.body.style.overflow = 'hidden';
  }

  // Đóng giỏ hàng
  closeCart() {
    const cartContainer = document.querySelector('.cart-container');
    const overlay = document.querySelector('.cart-overlay');
    
    if (cartContainer) cartContainer.classList.remove('active');
    if (overlay) overlay.style.display = 'none';
    document.body.style.overflow = '';
  }

  // Xóa toàn bộ giỏ hàng
  clearCart() {
    this.cart = [];
    this.saveCart();
    this.updateCartDisplay();
  }

  // Hiển thị thông báo
  showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${type === 'success' ? '#4CAF50' : '#f44336'};
      color: white;
      padding: 12px 20px;
      border-radius: 4px;
      z-index: 10000;
      animation: slideIn 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.remove();
    }, 3000);
  }
}

// Khởi tạo cart manager khi DOM loaded
let cartManager;
document.addEventListener('DOMContentLoaded', () => {
  cartManager = new CartManager();
});

// Hàm global để thêm vào giỏ hàng (để sử dụng từ các file khác)
function addToCart(id, title, price, img, quantity = 1, note = '') {
  if (cartManager) {
    cartManager.addToCart({ id, title, price, img, quantity, note });
  }
}

// Hàm global để mở giỏ hàng
function openCart() {
  if (cartManager) {
    cartManager.openCart();
  }
}

// Hàm global để đóng giỏ hàng
function closeCart() {
  if (cartManager) {
    cartManager.closeCart();
  }
}
