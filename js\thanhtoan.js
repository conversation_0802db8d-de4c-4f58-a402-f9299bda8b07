/**
 * <PERSON><PERSON><PERSON> quản lý thanh toán
 * - <PERSON><PERSON> lý quy trình thanh toán
 * - Quản lý đơn hàng
 * - <PERSON>ể<PERSON> tra thông tin giao hàng
 */

// Biến lưu trữ thông tin giao hàng
let shippingInfo = JSON.parse(localStorage.getItem('shippingInfo')) || {};
let paymentMethod = 'cash'; // Mặc định là thanh toán khi nhận hàng
let currentOrder = null;

// API Endpoints - sử dụng JSON server hoặc API tùy chỉnh
const API_BASE_URL = "https://json-api-service.onrender.com";
const ORDERS_API = API_BASE_URL + "/orders";

document.addEventListener('DOMContentLoaded', function() {
  // Kiểm tra kết nối với API
  checkApiConnection();
  
  // Khởi tạo sự kiện cho các nút thanh toán
  initCheckoutEvents();
  
  // Kiểm tra và hiển thị các phương thức thanh toán
  setupPaymentMethods();
  
  // Cập nhật thông tin từ localStorage
  updateShippingInfo();
});

/**
 * Kiểm tra kết nối với API
 */
function checkApiConnection() {
  fetch(API_BASE_URL)
    .then(response => {
      if (response.ok) {
        console.log("Kết nối API thành công!");
      } else {
        console.warn("API trả về lỗi:", response.status);
        // Lưu đơn hàng vào localStorage nếu không thể kết nối đến API
        setupLocalStorageFallback();
      }
    })
    .catch(error => {
      console.error("Không thể kết nối đến API:", error);
      // Lưu đơn hàng vào localStorage nếu không thể kết nối đến API
      setupLocalStorageFallback();
    });
}

/**
 * Thiết lập lưu trữ local nếu API không khả dụng
 */
function setupLocalStorageFallback() {
  // Đảm bảo có một mảng đơn hàng trong localStorage
  if (!localStorage.getItem('orders')) {
    localStorage.setItem('orders', JSON.stringify([]));
  }
}

/**
 * Khởi tạo sự kiện cho quy trình thanh toán
 */
function initCheckoutEvents() {
  // Nút thanh toán trên giỏ hàng
  const checkoutBtn = document.querySelector('.cart-checkout');
  if (checkoutBtn) {
    checkoutBtn.addEventListener('click', openCheckoutModal);
  }
  
  // Đóng modal thanh toán
  document.addEventListener('click', function(e) {
    if (e.target.matches('.checkout-modal .close-btn') || 
        e.target.matches('.checkout-modal .btn-cancel')) {
      closeCheckoutModal();
    }
    
    // Xử lý nút trở lại bước trước
    if (e.target.matches('.btn-back')) {
      goToPreviousStep();
    }
    
    // Xử lý nút tiếp tục
    if (e.target.matches('.btn-continue')) {
      goToNextStep();
    }
    
    // Xử lý nút xác nhận đơn hàng
    if (e.target.matches('.btn-place-order')) {
      placeOrder();
    }
  });
  
  // Xử lý khi thay đổi phương thức thanh toán
  document.addEventListener('change', function(e) {
    if (e.target.name === 'payment-method') {
      paymentMethod = e.target.value;
      updatePaymentSummary();
    }
  });
  
  // Xử lý khi nhập thông tin giao hàng
  const shippingForm = document.getElementById('shipping-form');
  if (shippingForm) {
    shippingForm.addEventListener('input', function(e) {
      if (['fullname', 'phone', 'address', 'province', 'district', 'ward'].includes(e.target.name)) {
        shippingInfo[e.target.name] = e.target.value;
        localStorage.setItem('shippingInfo', JSON.stringify(shippingInfo));
      }
    });
  }
}

/**
 * Mở modal thanh toán
 */
function openCheckoutModal() {
  const account = JSON.parse(localStorage.getItem('currentAccount'));
  if (!account) {
    alert('Vui lòng đăng nhập để thanh toán!');
    return;
  }
  
  const cart = JSON.parse(localStorage.getItem('cart')) || [];
  const userCart = cart.filter(item => item.userId === account.id);
  
  if (userCart.length === 0) {
    alert('Giỏ hàng của bạn đang trống!');
    return;
  }
  
  // Tạo modal thanh toán nếu chưa tồn tại
  let checkoutModal = document.getElementById('checkout-modal');
  if (!checkoutModal) {
    checkoutModal = createCheckoutModal();
    document.body.appendChild(checkoutModal);
  }
  
  // Hiển thị modal
  checkoutModal.style.display = 'flex';
  
  // Cập nhật thông tin thanh toán
  updateCheckoutItems(userCart);
  updateCheckoutTotal(userCart);
  
  // Hiển thị bước đầu tiên
  showCheckoutStep(1);
  
  // Đóng giỏ hàng nếu đang mở
  const cartContainer = document.querySelector('.cart-container');
  if (cartContainer && cartContainer.classList.contains('active')) {
    window.closeCart();
  }
}

/**
 * Tạo modal thanh toán
 */
function createCheckoutModal() {
  const modal = document.createElement('div');
  modal.id = 'checkout-modal';
  modal.className = 'checkout-modal';
  
  modal.innerHTML = `
    <div class="checkout-content">
      <span class="close-btn">&times;</span>
      
      <div class="checkout-header">
        <h2>Thanh toán</h2>
        <div class="checkout-steps">
          <div class="step active" data-step="1">
            <span class="step-number">1</span>
            <span class="step-text">Xác nhận giỏ hàng</span>
          </div>
          <div class="step" data-step="2">
            <span class="step-number">2</span>
            <span class="step-text">Thông tin giao hàng</span>
          </div>
          <div class="step" data-step="3">
            <span class="step-number">3</span>
            <span class="step-text">Phương thức thanh toán</span>
          </div>
          <div class="step" data-step="4">
            <span class="step-number">4</span>
            <span class="step-text">Xác nhận đơn hàng</span>
          </div>
        </div>
      </div>
      
      <div class="checkout-body">
        <!-- Bước 1: Xác nhận giỏ hàng -->
        <div class="checkout-step" id="step-1">
          <h3>Xác nhận giỏ hàng</h3>
          <div class="checkout-items">
            <!-- Các mục sẽ được thêm bằng JavaScript -->
          </div>
          <div class="checkout-summary">
            <div class="checkout-total">
              <span>Tổng tiền:</span>
              <span class="total-amount">0₫</span>
            </div>
          </div>
          <div class="checkout-actions">
            <button class="btn-cancel">Hủy</button>
            <button class="btn-continue">Tiếp tục</button>
          </div>
        </div>
        
        <!-- Bước 2: Thông tin giao hàng -->
        <div class="checkout-step" id="step-2" style="display: none;">
          <h3>Thông tin giao hàng</h3>
          <form id="shipping-form">
            <div class="form-group">
              <label for="fullname">Họ và tên</label>
              <input type="text" id="fullname" name="fullname" placeholder="Nhập họ và tên người nhận" required>
            </div>
            <div class="form-group">
              <label for="phone">Số điện thoại</label>
              <input type="tel" id="phone" name="phone" placeholder="Nhập số điện thoại" required>
            </div>
            <div class="form-group">
              <label for="address">Địa chỉ</label>
              <input type="text" id="address" name="address" placeholder="Số nhà, tên đường" required>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label for="province">Tỉnh/Thành phố</label>
                <select id="province" name="province" required>
                  <option value="">Chọn tỉnh/thành phố</option>
                  <option value="tp-ho-chi-minh">TP. Hồ Chí Minh</option>
                  <option value="ha-noi">Hà Nội</option>
                  <option value="da-nang">Đà Nẵng</option>
                  <option value="can-tho">Cần Thơ</option>
                  <option value="tra-vinh">Trà Vinh</option>
                  <!-- Thêm các tỉnh/thành phố khác -->
                </select>
              </div>
              <div class="form-group">
                <label for="district">Quận/Huyện</label>
                <select id="district" name="district" required>
                  <option value="">Chọn quận/huyện</option>
                </select>
              </div>
            </div>
            <div class="form-group">
              <label for="ward">Phường/Xã</label>
              <select id="ward" name="ward" required>
                <option value="">Chọn phường/xã</option>
              </select>
            </div>
            <div class="form-group">
              <label for="note">Ghi chú</label>
              <textarea id="shipping-note" name="note" placeholder="Thông tin thêm về đơn hàng"></textarea>
            </div>
          </form>
          <div class="checkout-actions">
            <button class="btn-back">Quay lại</button>
            <button class="btn-continue">Tiếp tục</button>
          </div>
        </div>
        
        <!-- Bước 3: Phương thức thanh toán -->
        <div class="checkout-step" id="step-3" style="display: none;">
          <h3>Phương thức thanh toán</h3>
          <div class="payment-methods">
            <div class="payment-method">
              <input type="radio" id="payment-cash" name="payment-method" value="cash" checked>
              <label for="payment-cash">
                <i class="fas fa-money-bill-wave"></i>
                <span>Thanh toán khi nhận hàng</span>
              </label>
            </div>
            <div class="payment-method">
              <input type="radio" id="payment-bank" name="payment-method" value="bank">
              <label for="payment-bank">
                <i class="fas fa-university"></i>
                <span>Chuyển khoản ngân hàng</span>
              </label>
            </div>
            <div class="payment-method">
              <input type="radio" id="payment-momo" name="payment-method" value="momo">
              <label for="payment-momo">
                <i class="fas fa-wallet"></i>
                <span>Ví MoMo</span>
              </label>
            </div>
            <div class="payment-method">
              <input type="radio" id="payment-credit" name="payment-method" value="credit">
              <label for="payment-credit">
                <i class="fas fa-credit-card"></i>
                <span>Thẻ tín dụng/ghi nợ</span>
              </label>
            </div>
          </div>
          <div class="bank-info" style="display: none;">
            <h4>Thông tin chuyển khoản</h4>
            <p><strong>Ngân hàng:</strong> Ngân hàng TMCP Ngoại thương Việt Nam (Vietcombank)</p>
            <p><strong>Số tài khoản:</strong> **********</p>
            <p><strong>Chủ tài khoản:</strong> NA FOOD</p>
            <p><strong>Nội dung:</strong> <span id="transfer-content">Thanh toan don hang</span></p>
          </div>
          <div class="checkout-actions">
            <button class="btn-back">Quay lại</button>
            <button class="btn-continue">Tiếp tục</button>
          </div>
        </div>
        
        <!-- Bước 4: Xác nhận đơn hàng -->
        <div class="checkout-step" id="step-4" style="display: none;">
          <h3>Xác nhận đơn hàng</h3>
          <div class="order-summary">
            <div class="summary-section">
              <h4>Thông tin giao hàng</h4>
              <p><strong>Họ tên:</strong> <span id="summary-name"></span></p>
              <p><strong>Số điện thoại:</strong> <span id="summary-phone"></span></p>
              <p><strong>Địa chỉ:</strong> <span id="summary-address"></span></p>
              <p><strong>Ghi chú:</strong> <span id="summary-note"></span></p>
            </div>
            <div class="summary-section">
              <h4>Phương thức thanh toán</h4>
              <p id="summary-payment-method">Thanh toán khi nhận hàng</p>
            </div>
            <div class="summary-section">
              <h4>Chi tiết đơn hàng</h4>
              <div class="summary-items">
                <!-- Sẽ được thêm bằng JavaScript -->
              </div>
              <div class="summary-totals">
                <div class="summary-subtotal">
                  <span>Tạm tính:</span>
                  <span id="summary-subtotal">0₫</span>
                </div>
                <div class="summary-shipping">
                  <span>Phí vận chuyển:</span>
                  <span id="summary-shipping">25,000₫</span>
                </div>
                <div class="summary-total">
                  <span>Tổng cộng:</span>
                  <span id="summary-total">0₫</span>
                </div>
              </div>
            </div>
          </div>
          <div class="checkout-actions">
            <button class="btn-back">Quay lại</button>
            <button class="btn-place-order">Đặt hàng</button>
          </div>
        </div>
      </div>
    </div>
  `;
  
  return modal;
}

/**
 * Đóng modal thanh toán
 */
function closeCheckoutModal() {
  const modal = document.getElementById('checkout-modal');
  if (modal) {
    modal.style.display = 'none';
  }
}

/**
 * Hiển thị bước thanh toán
 */
function showCheckoutStep(stepNumber) {
  // Ẩn tất cả các bước
  document.querySelectorAll('.checkout-step').forEach(step => {
    step.style.display = 'none';
  });
  
  // Hiển thị bước được chọn
  const targetStep = document.getElementById(`step-${stepNumber}`);
  if (targetStep) {
    targetStep.style.display = 'block';
  }
  
  // Cập nhật trạng thái của các bước
  document.querySelectorAll('.checkout-steps .step').forEach(step => {
    const stepNum = parseInt(step.getAttribute('data-step'));
    if (stepNum < stepNumber) {
      step.classList.remove('active');
      step.classList.add('completed');
    } else if (stepNum === stepNumber) {
      step.classList.add('active');
      step.classList.remove('completed');
    } else {
      step.classList.remove('active', 'completed');
    }
  });
  
  // Thực hiện các hành động đặc biệt cho từng bước
  if (stepNumber === 2) {
    populateShippingForm();
  } else if (stepNumber === 3) {
    // Hiển thị/ẩn thông tin ngân hàng dựa trên phương thức thanh toán
    updatePaymentMethodUI();
  } else if (stepNumber === 4) {
    updateOrderSummary();
  }
}

/**
 * Chuyển đến bước tiếp theo
 */
function goToNextStep() {
  const activeStep = document.querySelector('.checkout-steps .step.active');
  if (!activeStep) return;
  
  const currentStep = parseInt(activeStep.getAttribute('data-step'));
  
  // Kiểm tra điều kiện trước khi chuyển sang bước tiếp
  if (currentStep === 2) {
    // Kiểm tra form thông tin giao hàng
    const form = document.getElementById('shipping-form');
    if (form && !form.checkValidity()) {
      form.reportValidity();
      return;
    }
  }
  
  showCheckoutStep(currentStep + 1);
}

/**
 * Quay lại bước trước
 */
function goToPreviousStep() {
  const activeStep = document.querySelector('.checkout-steps .step.active');
  if (!activeStep) return;
  
  const currentStep = parseInt(activeStep.getAttribute('data-step'));
  if (currentStep > 1) {
    showCheckoutStep(currentStep - 1);
  }
}

/**
 * Cập nhật danh sách sản phẩm trong thanh toán
 */
function updateCheckoutItems(cartItems) {
  const checkoutItems = document.querySelector('.checkout-items');
  if (!checkoutItems) return;
  
  checkoutItems.innerHTML = '';
  
  cartItems.forEach(item => {
    const itemElement = document.createElement('div');
    itemElement.className = 'checkout-item';
    itemElement.innerHTML = `
      <div class="item-image">
        <img src="${item.productImg}" alt="${item.productName}">
      </div>
      <div class="item-info">
        <h4>${item.productName}</h4>
        <p>Đơn giá: ${formatPrice(item.price)}</p>
        <p>Số lượng: ${item.quantity}</p>
        ${item.note ? `<p>Ghi chú: ${item.note}</p>` : ''}
      </div>
      <div class="item-price">${formatPrice(item.price * item.quantity)}</div>
    `;
    checkoutItems.appendChild(itemElement);
  });
}

/**
 * Cập nhật tổng tiền trong thanh toán
 */
function updateCheckoutTotal(cartItems) {
  const totalElement = document.querySelector('.checkout-total .total-amount');
  if (!totalElement) return;
  
  const total = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  totalElement.textContent = formatPrice(total);
}

/**
 * Điền thông tin vào form giao hàng
 */
function populateShippingForm() {
  const form = document.getElementById('shipping-form');
  if (!form) return;
  
  const account = JSON.parse(localStorage.getItem('currentAccount'));
  if (!account) return;
  
  // Điền thông tin từ localStorage nếu có
  if (shippingInfo.fullname) form.fullname.value = shippingInfo.fullname;
  if (shippingInfo.phone) form.phone.value = shippingInfo.phone;
  if (shippingInfo.address) form.address.value = shippingInfo.address;
  
  // Điền thông tin địa chỉ
  if (shippingInfo.province) {
    form.province.value = shippingInfo.province;
    loadDistricts(shippingInfo.province);
    
    if (shippingInfo.district) {
      setTimeout(() => {
        form.district.value = shippingInfo.district;
        loadWards(shippingInfo.district);
        
        if (shippingInfo.ward) {
          setTimeout(() => {
            form.ward.value = shippingInfo.ward;
          }, 100);
        }
      }, 100);
    }
  }
  
  // Điền ghi chú nếu có
  if (shippingInfo.note) {
    document.getElementById('shipping-note').value = shippingInfo.note;
  }
}

/**
 * Cập nhật giao diện phương thức thanh toán
 */
function updatePaymentMethodUI() {
  const bankInfo = document.querySelector('.bank-info');
  if (!bankInfo) return;
  
  // Hiển thị thông tin ngân hàng nếu chọn chuyển khoản
  if (paymentMethod === 'bank') {
    bankInfo.style.display = 'block';
    // Tạo nội dung chuyển khoản
    const account = JSON.parse(localStorage.getItem('currentAccount'));
    if (account) {
      document.getElementById('transfer-content').textContent = `NAFOOD_${account.id}_${Date.now()}`;
    }
  } else {
    bankInfo.style.display = 'none';
  }
}

/**
 * Cập nhật thông tin tóm tắt đơn hàng
 */
function updateOrderSummary() {
  // Cập nhật thông tin giao hàng
  document.getElementById('summary-name').textContent = shippingInfo.fullname || '';
  document.getElementById('summary-phone').textContent = shippingInfo.phone || '';
  
  // Tạo địa chỉ đầy đủ
  const addressParts = [];
  if (shippingInfo.address) addressParts.push(shippingInfo.address);
  if (shippingInfo.ward) addressParts.push(getWardName(shippingInfo.ward));
  if (shippingInfo.district) addressParts.push(getDistrictName(shippingInfo.district));
  if (shippingInfo.province) addressParts.push(getProvinceName(shippingInfo.province));
  
  document.getElementById('summary-address').textContent = addressParts.join(', ');
  document.getElementById('summary-note').textContent = shippingInfo.note || 'Không có';
  
  // Cập nhật phương thức thanh toán
  let paymentText = 'Thanh toán khi nhận hàng';
  if (paymentMethod === 'bank') {
    paymentText = 'Chuyển khoản ngân hàng';
  } else if (paymentMethod === 'momo') {
    paymentText = 'Ví MoMo';
  } else if (paymentMethod === 'credit') {
    paymentText = 'Thẻ tín dụng/ghi nợ';
  }
  document.getElementById('summary-payment-method').textContent = paymentText;
  
  // Cập nhật chi tiết đơn hàng
  const cart = JSON.parse(localStorage.getItem('cart')) || [];
  const account = JSON.parse(localStorage.getItem('currentAccount'));
  if (!account) return;
  
  const userCart = cart.filter(item => item.userId === account.id);
  const summaryItems = document.querySelector('.summary-items');
  if (!summaryItems) return;
  
  summaryItems.innerHTML = '';
  userCart.forEach(item => {
    const itemElement = document.createElement('div');
    itemElement.className = 'summary-item';
    itemElement.innerHTML = `
      <div class="summary-item-name">${item.productName} x ${item.quantity}</div>
      <div class="summary-item-price">${formatPrice(item.price * item.quantity)}</div>
    `;
    summaryItems.appendChild(itemElement);
  });
  
  // Cập nhật tổng tiền
  const subtotal = userCart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const shipping = 25000; // Phí vận chuyển cố định
  const total = subtotal + shipping;
  
  document.getElementById('summary-subtotal').textContent = formatPrice(subtotal);
  document.getElementById('summary-total').textContent = formatPrice(total);
}

/**
 * Xử lý đặt hàng
 */
function placeOrder() {
  const account = JSON.parse(localStorage.getItem('currentAccount'));
  if (!account) {
    alert('Vui lòng đăng nhập để đặt hàng!');
    return;
  }
  
  const cart = JSON.parse(localStorage.getItem('cart')) || [];
  const userCart = cart.filter(item => item.userId === account.id);
  
  if (userCart.length === 0) {
    alert('Giỏ hàng của bạn đang trống!');
    return;
  }
  
  // Tạo mã đơn hàng
  const orderId = `NAF${Date.now()}`;
  
  // Tính toán giá trị đơn hàng
  const subtotal = userCart.reduce((sum, item) => sum + (item.total || item.price * item.quantity), 0);
  const shipping = 25000;
  const total = subtotal + shipping;
  
  // Tạo object đơn hàng
  const order = {
    id: orderId,
    userId: account.id,
    username: account.username,
    customerName: shippingInfo.fullname,
    phone: shippingInfo.phone,
    address: getFullAddress(),
    items: userCart.map(item => ({
      productId: item.productId,
      productName: item.productName,
      productImg: item.productImg,
      price: item.price,
      quantity: item.quantity,
      note: item.note || '',
      total: item.total || (item.price * item.quantity)
    })),
    subtotal: subtotal,
    shipping: shipping,
    total: total,
    paymentMethod: paymentMethod,
    paymentStatus: paymentMethod === 'cash' ? 'pending' : 'waiting',
    orderStatus: 'processing',
    orderDate: new Date().toISOString(),
    note: shippingInfo.note || '',
    updatedAt: new Date().toISOString()
  };
  
  // Lưu đơn hàng hiện tại
  currentOrder = order;
  
  // Xóa giỏ hàng và cập nhật localStorage trước để tránh trường hợp người dùng đóng trang
  const updatedCart = cart.filter(item => !(item.userId === account.id));
  localStorage.setItem('cart', JSON.stringify(updatedCart));
  
  // Tạm thời lưu vào localStorage
  const orderHistory = JSON.parse(localStorage.getItem('orderHistory')) || [];
  orderHistory.push(order);
  localStorage.setItem('orderHistory', JSON.stringify(orderHistory));
  
  // Cập nhật 'orders' trong localStorage (cho trường hợp không có API)
  const allOrders = JSON.parse(localStorage.getItem('orders')) || [];
  allOrders.push(order);
  localStorage.setItem('orders', JSON.stringify(allOrders));
  
  // Gửi sự kiện để thông báo đơn hàng mới được tạo
  // Điều này sẽ giúp các module khác có thể phản ứng với việc tạo đơn hàng
  const orderCreatedEvent = new CustomEvent('orderCreated', { detail: order });
  document.dispatchEvent(orderCreatedEvent);
  
  // Đồng bộ với API nếu có thể
  saveOrderToApi(order)
    .then(() => {
      // Đóng modal thanh toán
      closeCheckoutModal();
      
      // Hiển thị thông báo thành công
      showOrderConfirmation(order);
      
      // Cập nhật số lượng giỏ hàng
      if (typeof updateCartCount === 'function') {
        updateCartCount();
      }
      
      // Đồng bộ dữ liệu sau khi đặt hàng thành công
      if (typeof syncWithAPI === 'function') {
        console.log('Đang đồng bộ dữ liệu sau khi đặt hàng...');
        syncWithAPI();
      }
    })
    .catch(error => {
      console.error('Lỗi khi lưu đơn hàng vào API:', error);
      // Đơn hàng đã được lưu vào localStorage, vẫn hiển thị thông báo thành công
      closeCheckoutModal();
      showOrderConfirmation(order);
      
      // Cập nhật số lượng giỏ hàng
      if (typeof updateCartCount === 'function') {
        updateCartCount();
      }
      
      // Đồng bộ dữ liệu sau khi đặt hàng (ngay cả khi API lỗi)
      if (typeof syncWithAPI === 'function') {
        console.log('Đang đồng bộ dữ liệu sau khi đặt hàng (offline mode)...');
        syncWithAPI();
      }
    });
}

/**
 * Lưu đơn hàng vào API
 */
function saveOrderToApi(order) {
  return new Promise((resolve, reject) => {
    fetch(ORDERS_API, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(order)
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`Lỗi HTTP: ${response.status}`);
      }
      return response.json();
    })
    .then(data => {
      console.log('Đơn hàng đã được lưu vào API:', data);
      
      // Đánh dấu đơn hàng đã được đồng bộ
      const orderHistory = JSON.parse(localStorage.getItem('orderHistory')) || [];
      const updatedHistory = orderHistory.map(item => {
        if (item.id === order.id) {
          return { ...item, synced: true };
        }
        return item;
      });
      localStorage.setItem('orderHistory', JSON.stringify(updatedHistory));
      
      resolve(data);
    })
    .catch(error => {
      console.error('Không thể lưu đơn hàng vào API:', error);
      reject(error);
    });
  });
}

// Hàm trợ giúp để lấy text trạng thái đơn hàng
function getOrderStatusText(status) {
  switch (status) {
    case 'processing': return 'Đang xử lý';
    case 'confirmed': return 'Đã xác nhận';
    case 'shipping': return 'Đang giao hàng';
    case 'delivered': return 'Đã giao hàng';
    case 'cancelled': return 'Đã hủy';
    default: return 'Đang xử lý';
  }
}

// Hàm trợ giúp để lấy text phương thức thanh toán
function getPaymentMethodText(method) {
  switch (method) {
    case 'cash': return 'Thanh toán khi nhận hàng';
    case 'bank': return 'Chuyển khoản ngân hàng';
    case 'momo': return 'Ví MoMo';
    case 'credit': return 'Thẻ tín dụng/ghi nợ';
    default: return 'Thanh toán khi nhận hàng';
  }
}
/**
 * Hiển thị chi tiết một đơn hàng
 * @param {string} orderId - ID của đơn hàng cần hiển thị
 */
function showOrderDetail(orderId) {
  // Tìm đơn hàng từ localStorage
  const allOrders = JSON.parse(localStorage.getItem('orders')) || [];
  const orderHistory = JSON.parse(localStorage.getItem('orderHistory')) || [];
  
  // Kết hợp các nguồn dữ liệu
  const combinedOrders = [...allOrders, ...orderHistory];
  const order = combinedOrders.find(order => order.id === orderId);
  
  if (!order) {
    console.error('Không tìm thấy đơn hàng:', orderId);
    alert('Không tìm thấy thông tin đơn hàng');
    return;
  }
  
  // Thử gọi hàm viewOrderDetail từ taikhoan.js nếu có
  if (typeof viewOrderDetail === 'function') {
    viewOrderDetail(orderId);
    return;
  }
  
  // Fallback: Hiển thị thông tin đơn hàng trong alert nếu không có hàm viewOrderDetail
  const orderDate = new Date(order.orderDate);
  const formattedDate = orderDate.toLocaleDateString('vi-VN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
  
  let orderDetails = `Chi tiết đơn hàng #${order.id}\n`;
  orderDetails += `Ngày đặt: ${formattedDate}\n`;
  orderDetails += `Trạng thái: ${getOrderStatusText(order.orderStatus)}\n`;
  orderDetails += `Phương thức thanh toán: ${getPaymentMethodText(order.paymentMethod)}\n`;
  orderDetails += `Tổng tiền: ${formatPrice(order.total)}\n\n`;
  orderDetails += `Danh sách sản phẩm:\n`;
  
  order.items.forEach((item, index) => {
    orderDetails += `${index + 1}. ${item.productName} - ${item.quantity} x ${formatPrice(item.price)}\n`;
  });
  
  alert(orderDetails);
}

/**
 * Hiển thị thông báo xác nhận đặt hàng thành công
 */
function showOrderConfirmation(order) {
  // Tạo modal xác nhận đơn hàng
  let confirmationModal = document.getElementById('order-confirmation');
  
  // Xóa các event listener cũ nếu modal đã tồn tại
  const oldModal = confirmationModal;
  if (oldModal) {
    const newModal = oldModal.cloneNode(true);
    oldModal.parentNode.replaceChild(newModal, oldModal);
    confirmationModal = newModal;
  }
  
  if (!confirmationModal) {
    confirmationModal = document.createElement('div');
    confirmationModal.id = 'order-confirmation';
    confirmationModal.className = 'order-confirmation-modal';
    
    confirmationModal.innerHTML = `
      <div class="confirmation-content">
        <span class="close-btn">&times;</span>
        <div class="confirmation-header">
          <i class="fas fa-check-circle success-icon"></i>
          <h2>Đặt hàng thành công!</h2>
          <p>Cảm ơn bạn đã đặt hàng. Đơn hàng của bạn đã được tiếp nhận và đang được xử lý.</p>
        </div>
        
        <div class="confirmation-details">
          <div class="confirmation-info">
            <p><strong>Mã đơn hàng:</strong> <span id="confirmation-id">${order.id}</span></p>
            <p><strong>Ngày đặt:</strong> <span id="confirmation-date">${formatDate(order.orderDate)}</span></p>
            <p><strong>Tổng tiền:</strong> <span id="confirmation-total">${formatPrice(order.total)}</span></p>
            <p><strong>Phương thức thanh toán:</strong> <span id="confirmation-payment">${getPaymentMethodText(order.paymentMethod)}</span></p>
          </div>
          
          <div class="payment-instructions" ${order.paymentMethod !== 'bank' ? 'style="display:none"' : ''}>
            <h3>Hướng dẫn thanh toán</h3>
            <p>Vui lòng chuyển khoản trong vòng 24 giờ để đơn hàng được xử lý nhanh chóng.</p>
            <div class="bank-details">
              <p><strong>Ngân hàng:</strong> Ngân hàng TMCP Ngoại thương Việt Nam (Vietcombank)</p>
              <p><strong>Số tài khoản:</strong> **********</p>
              <p><strong>Chủ tài khoản:</strong> NA FOOD</p>
              <p><strong>Số tiền:</strong> ${formatPrice(order.total)}</p>
              <p><strong>Nội dung chuyển khoản:</strong> ${order.id}</p>
            </div>
          </div>
        </div>
        
        <div class="confirmation-actions">
          <a href="#" class="btn-track-order" data-orderid="${order.id}">Theo dõi đơn hàng</a>
          <a href="#" class="btn-view-orders">Xem tất cả đơn hàng</a>
          <a href="#" class="btn-continue-shopping">Tiếp tục mua sắm</a>
        </div>
      </div>
    `;
    
    document.body.appendChild(confirmationModal);
  } else {
    // Cập nhật thông tin đơn hàng
    confirmationModal.querySelector('#confirmation-id').textContent = order.id;
    confirmationModal.querySelector('#confirmation-date').textContent = formatDate(order.orderDate);
    confirmationModal.querySelector('#confirmation-total').textContent = formatPrice(order.total);
    confirmationModal.querySelector('#confirmation-payment').textContent = getPaymentMethodText(order.paymentMethod);
    
    // Cập nhật hướng dẫn thanh toán
    const paymentInstructions = confirmationModal.querySelector('.payment-instructions');
    if (paymentInstructions) {
      paymentInstructions.style.display = order.paymentMethod === 'bank' ? 'block' : 'none';
    }
    
    // Cập nhật nút theo dõi đơn hàng
    const trackOrderBtn = confirmationModal.querySelector('.btn-track-order');
    if (trackOrderBtn) {
      trackOrderBtn.setAttribute('data-orderid', order.id);
    }
  }
  
  // Xử lý sự kiện đóng modal
  confirmationModal.querySelector('.close-btn').addEventListener('click', function() {
    confirmationModal.style.display = 'none';
  });
  
  // Xử lý sự kiện theo dõi đơn hàng
  confirmationModal.querySelector('.btn-track-order').addEventListener('click', function(e) {
    e.preventDefault();
    confirmationModal.style.display = 'none';
    const orderId = this.getAttribute('data-orderid');
    showOrderDetail(orderId);
  });
  
  // Xử lý sự kiện xem tất cả đơn hàng
  confirmationModal.querySelector('.btn-view-orders').addEventListener('click', function(e) {
    e.preventDefault();
    confirmationModal.style.display = 'none';
    
    // Gọi hàm viewOrders từ taikhoan.js nếu có
    if (typeof viewOrders === 'function') {
      viewOrders();
    } else {
      console.warn('Hàm viewOrders không tồn tại');
      // Fallback: thông báo cho người dùng
      alert('Bạn có thể xem đơn hàng trong phần "Tài khoản > Đơn hàng của tôi"');
    }
  });
  
  // Xử lý sự kiện tiếp tục mua sắm
  confirmationModal.querySelector('.btn-continue-shopping').addEventListener('click', function(e) {
    e.preventDefault();
    confirmationModal.style.display = 'none';
    window.location.href = 'index.html';
  });
  
  // Hiển thị modal
  confirmationModal.style.display = 'flex';
  
  // Thông báo sự kiện đơn hàng đã được tạo
  const orderCreatedEvent = new CustomEvent('orderCreated', { 
    detail: { order } 
  });
  document.dispatchEvent(orderCreatedEvent);
}

/**
 * Hàm lấy địa chỉ đầy đủ
 */
function getFullAddress() {
  const addressParts = [];
  if (shippingInfo.address) addressParts.push(shippingInfo.address);
  if (shippingInfo.ward) addressParts.push(getWardName(shippingInfo.ward));
  if (shippingInfo.district) addressParts.push(getDistrictName(shippingInfo.district));
  if (shippingInfo.province) addressParts.push(getProvinceName(shippingInfo.province));
  
  return addressParts.join(', ');
}

/**
 * Cập nhật thông tin thanh toán
 */
function updatePaymentSummary() {
  const paymentMethodElement = document.getElementById('summary-payment-method');
  if (!paymentMethodElement) return;
  
  paymentMethodElement.textContent = getPaymentMethodText(paymentMethod);
}

/**
 * Lấy tên phương thức thanh toán
 */
function getPaymentMethodText(method) {
  switch(method) {
    case 'cash': return 'Thanh toán khi nhận hàng';
    case 'bank': return 'Chuyển khoản ngân hàng';
    case 'momo': return 'Ví MoMo';
    case 'credit': return 'Thẻ tín dụng/ghi nợ';
    default: return 'Thanh toán khi nhận hàng';
  }
}

/**
 * Thiết lập phương thức thanh toán
 */
function setupPaymentMethods() {
  // Có thể thêm logic để ẩn/hiện các phương thức thanh toán dựa trên cấu hình
}

/**
 * Cập nhật thông tin giao hàng từ localStorage
 */
function updateShippingInfo() {
  // Thông tin từ localStorage sẽ được sử dụng khi hiển thị form
}

/**
 * Hiển thị chi tiết đơn hàng
 */
function showOrderDetail(orderId) {
  // Tìm đơn hàng từ localStorage
  const orderHistory = JSON.parse(localStorage.getItem('orderHistory')) || [];
  const allOrders = JSON.parse(localStorage.getItem('orders')) || [];
  
  let order = orderHistory.find(order => order.id === orderId);
  if (!order) {
    order = allOrders.find(order => order.id === orderId);
  }
  
  if (!order) {
    // Nếu không tìm thấy trong localStorage, truy vấn API
    fetchOrderFromApi(orderId);
    return;
  }
  
  displayOrderDetails(order);
}

/**
 * Truy vấn thông tin đơn hàng từ API
 */
function fetchOrderFromApi(orderId) {
  fetch(`${ORDERS_API}/${orderId}`)
    .then(response => {
      if (!response.ok) {
        throw new Error('Không tìm thấy đơn hàng');
      }
      return response.json();
    })
    .then(order => {
      displayOrderDetails(order);
    })
    .catch(error => {
      console.error('Lỗi khi truy vấn đơn hàng:', error);
      alert('Không thể tìm thấy thông tin đơn hàng. Vui lòng thử lại sau.');
    });
}

/**
 * Hiển thị chi tiết đơn hàng trong modal
 */
function displayOrderDetails(order) {
  let orderDetailModal = document.getElementById('order-detail-modal');
  if (!orderDetailModal) {
    orderDetailModal = document.createElement('div');
    orderDetailModal.id = 'order-detail-modal';
    orderDetailModal.className = 'order-detail-modal';
    
    orderDetailModal.innerHTML = `
      <div class="order-detail-content">
        <span class="close-btn">&times;</span>
        <div class="order-detail-header">
          <h2>Chi tiết đơn hàng</h2>
          <div class="order-status">
            <span class="status-label">Trạng thái:</span>
            <span class="status-value"></span>
          </div>
          <div class="order-date">
            <span>Ngày đặt hàng:</span>
            <span id="detail-order-date"></span>
          </div>
        </div>
        
        <div class="order-detail-body">
          <div class="order-info">
            <div class="detail-section">
              <h3>Thông tin đơn hàng</h3>
              <p><strong>Mã đơn hàng:</strong> <span id="detail-order-id"></span></p>
              <p><strong>Khách hàng:</strong> <span id="detail-customer-name"></span></p>
              <p><strong>Số điện thoại:</strong> <span id="detail-phone"></span></p>
              <p><strong>Địa chỉ giao hàng:</strong> <span id="detail-address"></span></p>
              <p><strong>Ghi chú:</strong> <span id="detail-note"></span></p>
            </div>
            
            <div class="detail-section">
              <h3>Thông tin thanh toán</h3>
              <p><strong>Phương thức thanh toán:</strong> <span id="detail-payment-method"></span></p>
              <p><strong>Trạng thái thanh toán:</strong> <span id="detail-payment-status"></span></p>
              <p><strong>Tạm tính:</strong> <span id="detail-subtotal"></span></p>
              <p><strong>Phí vận chuyển:</strong> <span id="detail-shipping"></span></p>
              <p><strong>Tổng cộng:</strong> <span id="detail-total"></span></p>
            </div>
          </div>
          
          <div class="order-items">
            <h3>Sản phẩm</h3>
            <div class="detail-items-list">
              <!-- Danh sách sản phẩm sẽ được thêm vào đây -->
            </div>
          </div>
          
          <div class="order-timeline">
            <h3>Tiến trình đơn hàng</h3>
            <div class="timeline">
              <!-- Tiến trình sẽ được thêm vào đây -->
            </div>
          </div>
        </div>
        
        <div class="order-detail-actions">
          <button class="btn-close-detail">Đóng</button>
        </div>
      </div>
    `;
    
    document.body.appendChild(orderDetailModal);
    
    // Xử lý sự kiện đóng modal
    orderDetailModal.querySelector('.close-btn').addEventListener('click', function() {
      orderDetailModal.style.display = 'none';
    });
    
    orderDetailModal.querySelector('.btn-close-detail').addEventListener('click', function() {
      orderDetailModal.style.display = 'none';
    });
  }
  
  // Cập nhật thông tin đơn hàng
  orderDetailModal.querySelector('#detail-order-id').textContent = order.id;
  orderDetailModal.querySelector('#detail-customer-name').textContent = order.customerName;
  orderDetailModal.querySelector('#detail-phone').textContent = order.phone;
  orderDetailModal.querySelector('#detail-address').textContent = order.address;
  orderDetailModal.querySelector('#detail-note').textContent = order.note || 'Không có';
  
  orderDetailModal.querySelector('#detail-payment-method').textContent = getPaymentMethodText(order.paymentMethod);
  
  // Hiển thị trạng thái thanh toán
  let paymentStatusText = 'Chưa thanh toán';
  if (order.paymentStatus === 'paid') {
    paymentStatusText = 'Đã thanh toán';
  } else if (order.paymentStatus === 'waiting') {
    paymentStatusText = 'Chờ thanh toán';
  }
  orderDetailModal.querySelector('#detail-payment-status').textContent = paymentStatusText;
  
  // Hiển thị giá tiền
  orderDetailModal.querySelector('#detail-subtotal').textContent = formatPrice(order.subtotal);
  orderDetailModal.querySelector('#detail-shipping').textContent = formatPrice(order.shipping);
  orderDetailModal.querySelector('#detail-total').textContent = formatPrice(order.total);
  
  // Hiển thị ngày đặt hàng
  orderDetailModal.querySelector('#detail-order-date').textContent = formatDate(order.orderDate);
  
  // Hiển thị trạng thái đơn hàng
  const statusValue = orderDetailModal.querySelector('.status-value');
  statusValue.textContent = getOrderStatusText(order.orderStatus);
  statusValue.className = 'status-value status-' + order.orderStatus;
  
  // Hiển thị danh sách sản phẩm
  const itemsList = orderDetailModal.querySelector('.detail-items-list');
  itemsList.innerHTML = '';
  
  order.items.forEach(item => {
    const itemElement = document.createElement('div');
    itemElement.className = 'detail-item';
    itemElement.innerHTML = `
      <div class="detail-item-image">
        <img src="${item.productImg}" alt="${item.productName}">
      </div>
      <div class="detail-item-info">
        <h4>${item.productName}</h4>
        <p>Số lượng: ${item.quantity}</p>
        <p>Đơn giá: ${formatPrice(item.price)}</p>
        ${item.note ? `<p>Ghi chú: ${item.note}</p>` : ''}
      </div>
      <div class="detail-item-total">
        ${formatPrice(item.total || item.price * item.quantity)}
      </div>
    `;
    itemsList.appendChild(itemElement);
  });
  
  // Hiển thị tiến trình đơn hàng
  const timeline = orderDetailModal.querySelector('.timeline');
  timeline.innerHTML = '';
  
  // Các trạng thái đơn hàng
  const statuses = [
    { code: 'processing', text: 'Đang xử lý', time: order.orderDate },
    { code: 'confirmed', text: 'Đã xác nhận', time: order.confirmedAt },
    { code: 'shipping', text: 'Đang giao hàng', time: order.shippingAt },
    { code: 'delivered', text: 'Đã giao hàng', time: order.deliveredAt },
    { code: 'completed', text: 'Hoàn thành', time: order.completedAt }
  ];
  
  // Hiển thị các trạng thái đã đạt được
  const currentStatusIndex = statuses.findIndex(status => status.code === order.orderStatus);
  
  statuses.forEach((status, index) => {
    const isActive = index <= currentStatusIndex;
    const timelineItem = document.createElement('div');
    timelineItem.className = `timeline-item ${isActive ? 'active' : ''}`;
    
    timelineItem.innerHTML = `
      <div class="timeline-icon"></div>
      <div class="timeline-content">
        <h4>${status.text}</h4>
        ${status.time ? `<p>${formatDate(status.time)}</p>` : ''}
      </div>
    `;
    
    timeline.appendChild(timelineItem);
  });
  
  // Hiển thị modal
  orderDetailModal.style.display = 'flex';
}

/**
 * Lấy text hiển thị cho trạng thái đơn hàng
 */
function getOrderStatusText(statusCode) {
  switch(statusCode) {
    case 'processing': return 'Đang xử lý';
    case 'confirmed': return 'Đã xác nhận';
    case 'shipping': return 'Đang giao hàng';
    case 'delivered': return 'Đã giao hàng';
    case 'completed': return 'Hoàn thành';
    case 'cancelled': return 'Đã hủy';
    default: return 'Đang xử lý';
  }
}

/**
 * Đồng bộ hóa đơn hàng từ localStorage lên API
 */
function syncOrdersToApi() {
  const orderHistory = JSON.parse(localStorage.getItem('orderHistory')) || [];
  const unsyncedOrders = orderHistory.filter(order => !order.synced);
  
  if (unsyncedOrders.length === 0) {
    return Promise.resolve('Không có đơn hàng cần đồng bộ');
  }
  
  const syncPromises = unsyncedOrders.map(order => saveOrderToApi(order));
  
  return Promise.allSettled(syncPromises)
    .then(results => {
      const successful = results.filter(result => result.status === 'fulfilled').length;
      const failed = results.filter(result => result.status === 'rejected').length;
      
      console.log(`Đồng bộ đơn hàng: ${successful} thành công, ${failed} thất bại`);
      return `Đồng bộ đơn hàng: ${successful} thành công, ${failed} thất bại`;
    });
}

/**
 * Utility function: Format giá tiền
 */
function formatPrice(price) {
  return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(price);
}

/**
 * Utility function: Format ngày tháng
 */
function formatDate(dateString) {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('vi-VN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
}

/**
 * Utility function: Lấy tên tỉnh/thành phố từ giá trị
 */
function getProvinceName(provinceValue) {
  const provinceMappings = {
    'tp-ho-chi-minh': 'TP. Hồ Chí Minh',
    'ha-noi': 'Hà Nội',
    'da-nang': 'Đà Nẵng',
    'can-tho': 'Cần Thơ',
    'tra-vinh': 'Trà Vinh'
  };
  
  return provinceMappings[provinceValue] || provinceValue;
}

/**
 * Utility function: Lấy tên quận/huyện từ giá trị
 */
function getDistrictName(districtValue) {
  // Đơn giản hóa, trong thực tế cần có một bảng ánh xạ đầy đủ
  return districtValue;
}

/**
 * Utility function: Lấy tên phường/xã từ giá trị
 */
function getWardName(wardValue) {
  // Đơn giản hóa, trong thực tế cần có một bảng ánh xạ đầy đủ
  return wardValue;
}

/**
 * Load danh sách quận/huyện dựa trên tỉnh/thành phố
 */
function loadDistricts(provinceValue) {
  const districtSelect = document.getElementById('district');
  if (!districtSelect) return;
  
  // Xóa các option cũ
  districtSelect.innerHTML = '<option value="">Chọn quận/huyện</option>';
  
  // Tạo các giá trị mẫu
  const districts = [];
  
  if (provinceValue === 'tp-ho-chi-minh') {
    districts.push({ value: 'quan-1', text: 'Quận 1' });
    districts.push({ value: 'quan-2', text: 'Quận 2' });
    districts.push({ value: 'quan-3', text: 'Quận 3' });
    districts.push({ value: 'quan-4', text: 'Quận 4' });
    districts.push({ value: 'quan-5', text: 'Quận 5' });
  } else if (provinceValue === 'ha-noi') {
    districts.push({ value: 'ba-dinh', text: 'Ba Đình' });
    districts.push({ value: 'hoan-kiem', text: 'Hoàn Kiếm' });
    districts.push({ value: 'dong-da', text: 'Đống Đa' });
    districts.push({ value: 'hai-ba-trung', text: 'Hai Bà Trưng' });
  } else if (provinceValue === 'da-nang') {
    districts.push({ value: 'hai-chau', text: 'Hải Châu' });
    districts.push({ value: 'thanh-khe', text: 'Thanh Khê' });
    districts.push({ value: 'son-tra', text: 'Sơn Trà' });
  } else if (provinceValue === 'can-tho') {
    districts.push({ value: 'ninh-kieu', text: 'Ninh Kiều' });
    districts.push({ value: 'binh-thuy', text: 'Bình Thủy' });
    districts.push({ value: 'cai-rang', text: 'Cái Răng' });
  } else if (provinceValue === 'tra-vinh') {
    districts.push({ value: 'thanh-pho-tra-vinh', text: 'TP Trà Vinh' });
    districts.push({ value: 'cau-ke', text: 'Càu Kè' });
    districts.push({ value: 'tieu-can', text: 'Tiểu Cần' });
  }
  
  // Thêm các option mới
  districts.forEach(district => {
    const option = document.createElement('option');
    option.value = district.value;
    option.textContent = district.text;
    districtSelect.appendChild(option);
  });
}

/**
 * Load danh sách phường/xã dựa trên quận/huyện
 */
function loadWards(districtValue) {
  const wardSelect = document.getElementById('ward');
  if (!wardSelect) return;
  
  // Xóa các option cũ
  wardSelect.innerHTML = '<option value="">Chọn phường/xã</option>';
  
  // Tạo các giá trị mẫu
  const wards = [];
  
  // Thêm vài phường/xã mẫu
  for (let i = 1; i <= 5; i++) {
    wards.push({ value: `phuong-${i}`, text: `Phường ${i}` });
  }
  
  // Thêm các option mới
  wards.forEach(ward => {
    const option = document.createElement('option');
    option.value = ward.value;
    option.textContent = ward.text;
    wardSelect.appendChild(option);
  });
}

// Kết nối sự kiện cho select tỉnh/thành phố và quận/huyện
document.addEventListener('DOMContentLoaded', function() {
  const provinceSelect = document.getElementById('province');
  if (provinceSelect) {
    provinceSelect.addEventListener('change', function() {
      loadDistricts(this.value);
      shippingInfo.province = this.value;
      localStorage.setItem('shippingInfo', JSON.stringify(shippingInfo));
    });
  }
  
  const districtSelect = document.getElementById('district');
  if (districtSelect) {
    districtSelect.addEventListener('change', function() {
      loadWards(this.value);
      shippingInfo.district = this.value;
      localStorage.setItem('shippingInfo', JSON.stringify(shippingInfo));
    });
  }
  
  const wardSelect = document.getElementById('ward');
  if (wardSelect) {
    wardSelect.addEventListener('change', function() {
      shippingInfo.ward = this.value;
      localStorage.setItem('shippingInfo', JSON.stringify(shippingInfo));
    });
  }
});

/**
 * Thêm các hàm và biến toàn cục cho các module khác sử dụng
 */
window.openCheckoutModal = openCheckoutModal;
window.checkoutCart = openCheckoutModal; // Ghi đè hàm checkoutCart từ giohang.js