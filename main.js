const jsonServer = require('json-server');
const server = jsonServer.create();
const router = jsonServer.router('db.json'); // Đường dẫn tới tệp dữ liệu của bạn
const middlewares = jsonServer.defaults();

server.use(middlewares);
server.use(jsonServer.bodyParser);

// Thêm mã xử lý tùy chỉnh nếu cần
server.post('/products', (req, res, next) => {
    req.body.createdAt = Date.now();
    req.body.updatedAt = Date.now();

    res.status(201).send(req.body);
    next();

});

server.use(router);
server.listen(3000, () => {
    console.log('JSON Server is running');
});