/* Admin Panel CSS - Save this to acsets/css/admin.css */

:root {
    --primary-color: #ff6b6b;
    --secondary-color: #ff8e8e;
    --dark-color: #333;
    --light-color: #f4f4f4;
    --danger-color: #dc3545;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --sidebar-width: 250px;
    --header-height: 60px;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f0f2f5;
    color: var(--dark-color);
}

.admin-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Styles */
.sidebar {
    width: var(--sidebar-width);
    background-color: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    height: 100%;
    z-index: 1000;
    transition: var(--transition);
}

.sidebar .logo {
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #eee;
}

.sidebar .logo img {
    width: 40px;
    height: 40px;
    margin-right: 10px;
}

.sidebar .logo-text {
    color: var(--primary-color);
    font-family: 'Lobster', cursive;
    font-size: 24px;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin-top: 20px;
}

.sidebar-item {
    padding: 15px 20px;
    display: flex;
    align-items: center;
    color: var(--dark-color);
    cursor: pointer;
    transition: var(--transition);
}

.sidebar-item:hover {
    background-color: #f9f9f9;
    color: var(--primary-color);
}

.sidebar-item.active {
    background-color: #f5f5f5;
    color: var(--primary-color);
    border-left: 4px solid var(--primary-color);
}

.sidebar-item i {
    margin-right: 15px;
    font-size: 18px;
}

.sidebar-footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 20px;
    border-top: 1px solid #eee;
}

#logout-btn {
    width: 100%;
    padding: 10px;
    background-color: transparent;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    color: var(--dark-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

#logout-btn:hover {
    background-color: #f9f9f9;
    color: var(--danger-color);
}

#logout-btn i {
    margin-right: 10px;
}

/* Main Content Styles */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    transition: var(--transition);
}

.admin-header {
    height: var(--header-height);
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    position: sticky;
    top: 0;
    z-index: 900;
}

.header-left {
    display: flex;
    align-items: center;
}

#toggle-sidebar {
    background: transparent;
    border: none;
    font-size: 20px;
    margin-right: 20px;
    cursor: pointer;
    color: var(--dark-color);
}

.admin-profile {
    display: flex;
    align-items: center;
}

.admin-name {
    margin-right: 10px;
    font-weight: 500;
}

.admin-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

/* Page Content */
.page-content {
    padding: 20px;
    display: none;
}

.page-content.active {
    display: block;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.add-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: var(--transition);
}

.add-btn:hover {
    background-color: var(--secondary-color);
}

.add-btn i {
    margin-right: 5px;
}

/* Dashboard Cards */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.stat-card {
    background-color: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(255, 107, 107, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.stat-icon i {
    font-size: 24px;
    color: var(--primary-color);
}

.stat-info h3 {
    font-size: 14px;
    color: #777;
    margin-bottom: 5px;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--dark-color);
}

/* Tables */
.data-table {
    width: 100%;
    background-color: #fff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    border-collapse: collapse;
}

.data-table thead {
    background-color: #f8f9fa;
}

.data-table th,
.data-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.data-table tbody tr:hover {
    background-color: #f9f9f9;
}

/* Status badges */
.status {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.status.pending {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.status.confirmed {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
}

.status.shipping {
    background-color: rgba(0, 123, 255, 0.1);
    color: #007bff;
}

.status.delivered {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.status.cancelled {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

/* Filter Section */
.filter-section {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.search-input, .filter-select {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    flex: 1;
}

.filter-btn {
    padding: 10px 15px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.filter-btn:hover {
    background-color: var(--secondary-color);
}

/* Recent Orders Section */
.recent-section {
    background-color: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
    margin-top: 20px;
}

.recent-section h3 {
    margin-bottom: 15px;
    color: var(--dark-color);
}

/* Charts */
.stats-section {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
}

.stats-card {
    background-color: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
}

.stats-card h3 {
    margin-bottom: 15px;
    color: var(--dark-color);
}

.chart-container {
    height: 300px;
}

/* Modals */
.modal {
    display: none;
    position: fixed;
    z-index: 1100;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: #fff;
    margin: 50px auto;
    width: 80%;
    max-width: 600px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
    from {transform: translateY(-50px); opacity: 0;}
    to {transform: translateY(0); opacity: 1;}
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
}

.close-modal {
    font-size: 24px;
    cursor: pointer;
    color: #aaa;
}

.close-modal:hover {
    color: var(--dark-color);
}

.modal-body {
    padding: 20px;
}

/* Form Styles */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group input[type="email"],
.form-group input[type="password"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
}

.form-group textarea {
    resize: vertical;
}

.radio-group {
    display: flex;
    gap: 15px;
}

.radio-group input[type="radio"] {
    margin-right: 5px;
}

.form-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.cancel-btn, .save-btn {
    padding: 10px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.cancel-btn {
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    color: var(--dark-color);
}

.save-btn {
    background-color: var(--primary-color);
    border: none;
    color: white;
}

.cancel-btn:hover {
    background-color: #e0e0e0;
}

.save-btn:hover {
    background-color: var(--secondary-color);
}

/* Order Detail Styles */
.order-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.order-info, .customer-info {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: var(--border-radius);
}

.order-info h4, .customer-info h4, .order-items h4, .order-actions h4 {
    margin-bottom: 10px;
    color: var(--dark-color);
}

.order-items {
    margin-bottom: 20px;
}

.order-actions {
    margin-top: 20px;
}

.status-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.status-btn {
    padding: 8px 15px;
    border-radius: 20px;
    border: none;
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
}

.status-btn.pending {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
    border: 1px solid var(--warning-color);
}

.status-btn.confirmed {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
    border: 1px solid var(--info-color);
}

.status-btn.shipping {
    background-color: rgba(0, 123, 255, 0.1);
    color: #007bff;
    border: 1px solid #007bff;
}

.status-btn.delivered {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.status-btn.cancelled {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
    border: 1px solid var(--danger-color);
}

.status-btn:hover {
    filter: brightness(90%);
}

.text-right {
    text-align: right;
}

/* Image Preview */
.image-preview {
    margin-top: 10px;
    width: 100px;
    height: 100px;
    border: 1px dashed #ddd;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.image-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        width: 0;
        overflow: hidden;
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .sidebar.active {
        width: var(--sidebar-width);
    }
    
    .stats-cards, .stats-section {
        grid-template-columns: 1fr;
    }
    
    .order-details {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
    }
}