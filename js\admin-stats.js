/**
 * <PERSON><PERSON><PERSON><PERSON> lý thống kê cho Admin Dashboard
 * - <PERSON><PERSON><PERSON> thị tổng quan doanh thu, đ<PERSON><PERSON> hàng, s<PERSON><PERSON> phẩm, kh<PERSON>ch hàng
 * - Tạo biểu đồ doanh thu theo tháng
 * - Tạo biểu đồ sản phẩm bán chạy
 * - <PERSON><PERSON><PERSON> thị danh sách đơn hàng gần đây
 */

// Biểu đồ doanh thu và sản phẩm
let revenueChart = null;
let productChart = null;

document.addEventListener('DOMContentLoaded', function() {
  // Khởi tạo thống kê khi trang được tải
  initStats();
  
  // Xử lý nút làm mới thống kê
  const refreshStatsBtn = document.getElementById('refresh-stats-btn');
  if (refreshStatsBtn) {
    refreshStatsBtn.addEventListener('click', function() {
      this.classList.add('loading');
      this.disabled = true;
      
      // <PERSON><PERSON>m mới thống kê
      updateStats()
        .then(() => {
          this.classList.remove('loading');
          this.disabled = false;
          showNotification('Đã cập nhật thống kê', 'success');
        })
        .catch(error => {
          console.error('Lỗi khi cập nhật thống kê:', error);
          this.classList.remove('loading');
          this.disabled = false;
          showNotification('Không thể cập nhật thống kê: ' + error.message, 'error');
        });
    });
  }
  
  // Lắng nghe sự kiện thay đổi đơn hàng để cập nhật thống kê
  document.addEventListener('orderCreated', function(event) {
    console.log('Thống kê: Đã nhận sự kiện đơn hàng mới:', event.detail.order);
    updateStats();
  });
  
  document.addEventListener('orderUpdated', function(event) {
    console.log('Thống kê: Đã nhận sự kiện cập nhật đơn hàng:', event.detail.order);
    updateStats();
  });
});

/**
 * Khởi tạo thống kê
 */
function initStats() {
  // Tải thống kê ban đầu
  updateStats();
  
  // Khởi tạo biểu đồ doanh thu
  initRevenueChart();
  
  // Khởi tạo biểu đồ sản phẩm
  initProductChart();
}

/**
 * Cập nhật tất cả thống kê
 */
function updateStats() {
  return getAllOrdersData()
    .then(orders => {
      updateOverviewStats(orders);
      updateRecentOrders(orders);
      updateRevenueChart(orders);
      
      return getAllProductsData();
    })
    .then(products => {
      updateProductStats(products);
      return getAllUsersData();
    })
    .then(users => {
      updateUserStats(users);
      return true;
    })
    .catch(error => {
      console.error('Lỗi khi cập nhật thống kê:', error);
      showNotification('Đang hiển thị dữ liệu thống kê ngoại tuyến', 'info');
    });
}

/**
 * Lấy dữ liệu đơn hàng từ tất cả các nguồn
 */
function getAllOrdersData() {
  return new Promise((resolve, reject) => {
    // Thử tải từ API
    fetch('http://localhost:3000/orders')
      .then(response => response.json())
      .then(apiOrders => {
        // Lấy dữ liệu từ localStorage
        const localOrders = JSON.parse(localStorage.getItem('orders')) || [];
        const orderHistory = JSON.parse(localStorage.getItem('orderHistory')) || [];
        const cachedOrders = JSON.parse(localStorage.getItem('allOrdersCache')) || [];
        
        // Kết hợp tất cả các nguồn dữ liệu
        const allOrdersMap = new Map();
        
        [...apiOrders, ...cachedOrders, ...localOrders, ...orderHistory].forEach(order => {
          allOrdersMap.set(order.id, order);
        });
        
        const allOrders = Array.from(allOrdersMap.values());
        
        // Sắp xếp đơn hàng theo thời gian (mới nhất lên đầu)
        allOrders.sort((a, b) => new Date(b.orderDate || b.createdAt || 0) - new Date(a.orderDate || a.createdAt || 0));
        
        // Lưu vào cache
        localStorage.setItem('statsOrdersCache', JSON.stringify(allOrders));
        
        resolve(allOrders);
      })
      .catch(error => {
        console.error('Lỗi khi tải đơn hàng từ API:', error);
        
        // Dùng cache nếu không kết nối được API
        const cachedOrders = JSON.parse(localStorage.getItem('statsOrdersCache')) || [];
        const localOrders = JSON.parse(localStorage.getItem('orders')) || [];
        const orderHistory = JSON.parse(localStorage.getItem('orderHistory')) || [];
        
        // Kết hợp với dữ liệu từ localStorage
        const allOrdersMap = new Map();
        
        [...cachedOrders, ...localOrders, ...orderHistory].forEach(order => {
          allOrdersMap.set(order.id, order);
        });
        
        const allOrders = Array.from(allOrdersMap.values());
        
        // Sắp xếp đơn hàng theo thời gian (mới nhất lên đầu)
        allOrders.sort((a, b) => new Date(b.orderDate || b.createdAt || 0) - new Date(a.orderDate || a.createdAt || 0));
        
        resolve(allOrders);
      });
  });
}

/**
 * Lấy dữ liệu sản phẩm từ tất cả các nguồn
 */
function getAllProductsData() {
  return new Promise((resolve, reject) => {
    // Lấy từ localStorage
    const products = JSON.parse(localStorage.getItem('products')) || [];
    resolve(products);
  });
}

/**
 * Lấy dữ liệu người dùng từ tất cả các nguồn
 */
function getAllUsersData() {
  return new Promise((resolve, reject) => {
    // Lấy từ localStorage
    const users = JSON.parse(localStorage.getItem('users')) || [];
    resolve(users);
  });
}

/**
 * Cập nhật thống kê tổng quan
 */
function updateOverviewStats(orders) {
  // Tính tổng doanh thu
  const totalRevenue = orders.reduce((total, order) => {
    if (order.orderStatus !== 'cancelled') {
      return total + (order.total || 0);
    }
    return total;
  }, 0);
  
  // Đếm đơn hàng
  const totalOrders = orders.length;
  const completedOrders = orders.filter(order => 
    order.orderStatus === 'delivered' || order.orderStatus === 'completed'
  ).length;
  
  // Cập nhật UI
  document.getElementById('total-revenue').textContent = formatCurrency(totalRevenue);
  document.getElementById('total-orders').textContent = totalOrders;
  document.getElementById('completed-orders').textContent = completedOrders;
  document.getElementById('revenue-update-time').textContent = formatDate(new Date());
}

/**
 * Cập nhật thống kê sản phẩm
 */
function updateProductStats(products) {
  const totalProducts = products.length;
  const activeProducts = products.filter(product => product.active !== false).length;
  
  // Cập nhật UI
  document.getElementById('total-products').textContent = totalProducts;
  document.getElementById('active-products').textContent = activeProducts;
}

/**
 * Cập nhật thống kê người dùng
 */
function updateUserStats(users) {
  const totalUsers = users.length;
  
  // Đếm người dùng mới trong tháng này
  const currentDate = new Date();
  const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
  
  const newUsers = users.filter(user => {
    const createdAt = new Date(user.createdAt || user.registeredDate || 0);
    return createdAt >= firstDayOfMonth;
  }).length;
  
  // Cập nhật UI
  document.getElementById('total-users').textContent = totalUsers;
  document.getElementById('new-users').textContent = newUsers;
}

/**
 * Cập nhật bảng đơn hàng gần đây
 */
function updateRecentOrders(orders) {
  const recentOrdersList = document.getElementById('recent-orders-list');
  if (!recentOrdersList) return;
  
  // Chỉ hiển thị 5 đơn hàng gần đây nhất
  const recentOrders = orders.slice(0, 5);
  
  if (recentOrders.length === 0) {
    recentOrdersList.innerHTML = `
      <tr>
        <td colspan="5" class="text-center">Chưa có đơn hàng nào</td>
      </tr>
    `;
    return;
  }
  
  let html = '';
  
  recentOrders.forEach(order => {
    const orderDate = new Date(order.orderDate || order.createdAt || 0);
    const formattedDate = formatDate(orderDate);
    
    // Lấy tên khách hàng
    let customerName = order.customerName || order.shippingInfo?.fullName || 'Khách hàng';
    
    // Lấy trạng thái đơn hàng
    let statusText = 'Đang xử lý';
    let statusClass = 'status-processing';
    
    switch (order.orderStatus) {
      case 'confirmed':
        statusText = 'Đã xác nhận';
        statusClass = 'status-confirmed';
        break;
      case 'shipping':
        statusText = 'Đang giao hàng';
        statusClass = 'status-shipping';
        break;
      case 'delivered':
        statusText = 'Đã giao hàng';
        statusClass = 'status-delivered';
        break;
      case 'cancelled':
        statusText = 'Đã hủy';
        statusClass = 'status-cancelled';
        break;
    }
    
    html += `
      <tr data-order-id="${order.id}">
        <td>${order.id}</td>
        <td>${customerName}</td>
        <td>${formattedDate}</td>
        <td>${formatCurrency(order.total || 0)}</td>
        <td><span class="order-status ${statusClass}">${statusText}</span></td>
      </tr>
    `;
  });
  
  recentOrdersList.innerHTML = html;
  
  // Thêm sự kiện click cho các hàng đơn hàng
  recentOrdersList.querySelectorAll('tr[data-order-id]').forEach(row => {
    row.addEventListener('click', function() {
      const orderId = this.getAttribute('data-order-id');
      openOrderDetails(orderId);
    });
  });
}

/**
 * Khởi tạo biểu đồ doanh thu
 */
function initRevenueChart() {
  const ctx = document.getElementById('revenue-chart');
  if (!ctx) return;
  
  // Tạo biểu đồ trống đầu tiên
  revenueChart = new Chart(ctx, {
    type: 'bar',
    data: {
      labels: [],
      datasets: [{
        label: 'Doanh thu',
        data: [],
        backgroundColor: 'rgba(78, 115, 223, 0.6)',
        borderColor: 'rgba(78, 115, 223, 1)',
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            callback: function(value) {
              return formatCurrency(value, false);
            }
          }
        }
      },
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              return formatCurrency(context.raw);
            }
          }
        }
      }
    }
  });
}

/**
 * Cập nhật biểu đồ doanh thu
 */
function updateRevenueChart(orders) {
  if (!revenueChart) return;
  
  // Tạo dữ liệu theo tháng
  const monthlyData = {};
  
  // Lấy 6 tháng gần nhất
  const today = new Date();
  
  for (let i = 5; i >= 0; i--) {
    const month = new Date(today.getFullYear(), today.getMonth() - i, 1);
    const monthKey = month.toISOString().substring(0, 7); // YYYY-MM
    monthlyData[monthKey] = 0;
  }
  
  // Tính doanh thu theo tháng
  orders.forEach(order => {
    // Bỏ qua đơn hàng đã hủy
    if (order.orderStatus === 'cancelled') return;
    
    const orderDate = new Date(order.orderDate || order.createdAt || 0);
    const monthKey = orderDate.toISOString().substring(0, 7); // YYYY-MM
    
    if (monthlyData.hasOwnProperty(monthKey)) {
      monthlyData[monthKey] += (order.total || 0);
    }
  });
  
  // Cập nhật dữ liệu cho biểu đồ
  const labels = [];
  const data = [];
  
  for (const monthKey in monthlyData) {
    const [year, month] = monthKey.split('-');
    labels.push(`Tháng ${month}/${year}`);
    data.push(monthlyData[monthKey]);
  }
  
  // Cập nhật biểu đồ
  revenueChart.data.labels = labels;
  revenueChart.data.datasets[0].data = data;
  revenueChart.update();
}

/**
 * Khởi tạo biểu đồ sản phẩm bán chạy
 */
function initProductChart() {
  const ctx = document.getElementById('product-chart');
  if (!ctx) return;
  
  // Tạo biểu đồ trống đầu tiên
  productChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
      labels: [],
      datasets: [{
        data: [],
        backgroundColor: [
          'rgba(78, 115, 223, 0.6)',
          'rgba(28, 200, 138, 0.6)',
          'rgba(246, 194, 62, 0.6)',
          'rgba(231, 74, 59, 0.6)',
          'rgba(54, 185, 204, 0.6)'
        ],
        borderColor: [
          'rgba(78, 115, 223, 1)',
          'rgba(28, 200, 138, 1)',
          'rgba(246, 194, 62, 1)',
          'rgba(231, 74, 59, 1)',
          'rgba(54, 185, 204, 1)'
        ],
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'right',
        }
      }
    }
  });
  
  // Cập nhật dữ liệu sản phẩm
  updateProductChart();
}

/**
 * Cập nhật biểu đồ sản phẩm bán chạy
 */
function updateProductChart() {
  if (!productChart) return;
  
  // Lấy dữ liệu đơn hàng
  getAllOrdersData()
    .then(orders => {
      // Đếm sản phẩm bán chạy
      const productCounts = {};
      
      orders.forEach(order => {
        // Bỏ qua đơn hàng đã hủy
        if (order.orderStatus === 'cancelled') return;
        
        // Đếm số lượng sản phẩm bán ra
        if (order.items && Array.isArray(order.items)) {
          order.items.forEach(item => {
            const productName = item.productName || item.name || 'Sản phẩm không xác định';
            const quantity = item.quantity || 1;
            
            if (!productCounts[productName]) {
              productCounts[productName] = 0;
            }
            
            productCounts[productName] += quantity;
          });
        }
      });
      
      // Sắp xếp sản phẩm theo số lượng bán ra
      const sortedProducts = Object.entries(productCounts)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5); // Chỉ lấy 5 sản phẩm bán chạy nhất
      
      // Tạo dữ liệu cho biểu đồ
      const labels = sortedProducts.map(([name]) => name);
      const data = sortedProducts.map(([, count]) => count);
      
      // Cập nhật biểu đồ
      productChart.data.labels = labels;
      productChart.data.datasets[0].data = data;
      productChart.update();
    })
    .catch(error => {
      console.error('Lỗi khi cập nhật biểu đồ sản phẩm:', error);
    });
}

/**
 * Định dạng số tiền thành định dạng tiền tệ
 */
function formatCurrency(amount, showSymbol = true) {
  const formatter = new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
    minimumFractionDigits: 0
  });
  
  let formatted = formatter.format(amount);
  
  if (!showSymbol) {
    formatted = formatted.replace('₫', '');
  }
  
  return formatted;
}

/**
 * Định dạng ngày tháng
 */
function formatDate(date) {
  return new Date(date).toLocaleDateString('vi-VN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
}