/* Styles cho trang thanh toán */

/* Modal Checkout */
.checkout-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  justify-content: center;
  align-items: center;
}

.checkout-content {
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  position: relative;
  padding: 20px;
}

.checkout-content .close-btn {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  transition: color 0.3s;
}

.checkout-content .close-btn:hover {
  color: #e74c3c;
}

/* Header Thanh toán */
.checkout-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.checkout-header h2 {
  font-size: 24px;
  color: #333;
  margin-bottom: 15px;
}

/* <PERSON><PERSON><PERSON> b<PERSON> thanh toán */
.checkout-steps {
  display: flex;
  justify-content: space-between;
  margin: 20px 0;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
  text-align: center;
}

.step::before {
  content: '';
  position: absolute;
  top: 20px;
  left: -50%;
  width: 100%;
  height: 2px;
  background-color: #ddd;
  z-index: 1;
}

.step:first-child::before {
  display: none;
}

.step.active .step-number {
  background-color: var(--primary-color, #FB9400);
  color: white;
}

.step.completed .step-number {
  background-color: #27ae60;
  color: white;
}

.step.completed::before {
  background-color: #27ae60;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #ddd;
  color: #333;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  margin-bottom: 10px;
  z-index: 2;
}

.step-text {
  font-size: 14px;
  color: #555;
}

/* Nội dung từng bước */
.checkout-body {
  padding: 20px 0;
}

.checkout-step {
  margin-bottom: 30px;
}

.checkout-step h3 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #333;
}

/* Danh sách sản phẩm trong checkout */
.checkout-items {
  margin-bottom: 20px;
}

.checkout-item {
  display: flex;
  padding: 15px 0;
  border-bottom: 1px solid #eee;
}

.item-image {
  width: 80px;
  height: 80px;
  overflow: hidden;
  border-radius: 5px;
  margin-right: 15px;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-info {
  flex: 1;
}

.item-info h4 {
  font-size: 16px;
  margin: 0 0 5px 0;
  color: #333;
}

.item-info p {
  margin: 5px 0;
  font-size: 14px;
  color: #666;
}

.item-price {
  font-weight: bold;
  color: #333;
  font-size: 16px;
  display: flex;
  align-items: center;
}

/* Tổng tiền */
.checkout-summary {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 5px;
  margin-top: 20px;
}

.checkout-total {
  display: flex;
  justify-content: space-between;
  font-size: 18px;
  font-weight: bold;
}

/* Form giao hàng */
#shipping-form {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 15px;
  transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: var(--primary-color, #FB9400);
  outline: none;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

/* Phương thức thanh toán */
.payment-methods {
  display: grid;
  gap: 15px;
  margin-bottom: 20px;
}

.payment-method {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s;
}

.payment-method:hover {
  background-color: #f9f9f9;
}

.payment-method input {
  margin-right: 15px;
}

.payment-method label {
  display: flex;
  align-items: center;
  cursor: pointer;
  width: 100%;
}

.payment-method i {
  margin-right: 10px;
  font-size: 20px;
  color: var(--primary-color, #FB9400);
}

.payment-method input:checked + label {
  font-weight: bold;
}

.payment-method:has(input:checked) {
  border-color: var(--primary-color, #FB9400);
  background-color: rgba(251, 148, 0, 0.05);
}

/* Thông tin ngân hàng */
.bank-info {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 5px;
  margin-top: 15px;
}

.bank-info h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.bank-info p {
  margin: 5px 0;
  font-size: 14px;
}

/* Buttons */
.checkout-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
}

.btn-cancel,
.btn-back,
.btn-continue,
.btn-place-order {
  padding: 12px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-weight: bold;
  border: none;
  transition: all 0.3s;
}

.btn-cancel {
  background-color: #f1f1f1;
  color: #333;
}

.btn-back {
  background-color: #f1f1f1;
  color: #333;
}

.btn-continue {
  background-color: var(--primary-color, #FB9400);
  color: white;
}

.btn-place-order {
  background-color: #27ae60;
  color: white;
  padding: 12px 30px;
}

.btn-cancel:hover,
.btn-back:hover {
  background-color: #e0e0e0;
}

.btn-continue:hover {
  background-color: #e08500;
}

.btn-place-order:hover {
  background-color: #219653;
}

/* Xác nhận đơn hàng */
.order-summary {
  margin-bottom: 30px;
}

.summary-section {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.summary-section h4 {
  font-size: 18px;
  margin-bottom: 15px;
  color: #333;
}

.summary-section p {
  margin: 8px 0;
  font-size: 15px;
  color: #555;
}

.summary-items {
  margin-bottom: 15px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  font-size: 15px;
}

.summary-totals {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.summary-subtotal,
.summary-shipping {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  color: #555;
}

.summary-total {
  display: flex;
  justify-content: space-between;
  font-weight: bold;
  font-size: 18px;
  margin-top: 10px;
  color: #333;
}

/* Modal xác nhận đơn hàng */
.order-confirmation-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1001;
  justify-content: center;
  align-items: center;
}

.confirmation-content {
  width: 90%;
  max-width: 600px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  position: relative;
  padding: 30px;
}

.confirmation-content .close-btn {
  position: absolute;
  top: 15px;
  right: 20px;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.confirmation-header {
  text-align: center;
  margin-bottom: 30px;
}

.confirmation-header .success-icon {
  font-size: 60px;
  color: #27ae60;
  margin-bottom: 15px;
  display: block;
}

.confirmation-header h2 {
  color: #27ae60;
  margin-bottom: 10px;
}

.confirmation-header p {
  color: #555;
  font-size: 16px;
}

.confirmation-details {
  margin-bottom: 30px;
}

.confirmation-info {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 20px;
}

.confirmation-info p {
  margin: 8px 0;
  font-size: 15px;
}

.payment-instructions {
  margin-top: 20px;
  padding: 15px;
  border: 1px dashed #ddd;
  border-radius: 5px;
}

.payment-instructions h3 {
  font-size: 18px;
  margin-bottom: 10px;
  color: #333;
}

.bank-details {
  margin-top: 10px;
}

.bank-details p {
  margin: 5px 0;
  font-size: 14px;
}

.confirmation-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
  margin-top: 20px;
}

.btn-track-order,
.btn-continue-shopping,
.btn-view-orders {
  padding: 12px 20px;
  border-radius: 5px;
  cursor: pointer;
  text-decoration: none;
  font-weight: bold;
  transition: all 0.3s;
  text-align: center;
}

.btn-track-order {
  background-color: var(--primary-color, #FB9400);
  color: white;
}

.btn-continue-shopping {
  background-color: #f1f1f1;
  color: #333;
}

.btn-track-order:hover {
  background-color: #e08500;
}

.btn-continue-shopping:hover {
  background-color: #e0e0e0;
}

.btn-view-orders {
  background-color: #4e73df;
  color: white;
}

.btn-view-orders:hover {
  background-color: #2e59d9;
}

/* Modal chi tiết đơn hàng */
.order-detail-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1001;
  justify-content: center;
  align-items: center;
}

.order-detail-content {
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  position: relative;
  padding: 30px;
}

.order-detail-content .close-btn {
  position: absolute;
  top: 15px;
  right: 20px;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.order-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
  flex-wrap: wrap;
}

.order-detail-header h2 {
  margin: 0;
  color: #333;
}

.order-status {
  display: flex;
  align-items: center;
}

.status-label {
  margin-right: 10px;
  font-weight: bold;
}

.status-value {
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 14px;
  font-weight: bold;
}

.status-processing {
  background-color: #f39c12;
  color: white;
}

.status-confirmed {
  background-color: #3498db;
  color: white;
}

.status-shipping {
  background-color: #9b59b6;
  color: white;
}

.status-delivered {
  background-color: #2ecc71;
  color: white;
}

.status-completed {
  background-color: #27ae60;
  color: white;
}

.status-cancelled {
  background-color: #e74c3c;
  color: white;
}

.order-date {
  font-size: 14px;
  color: #666;
  margin-top: 10px;
}

.order-detail-body {
  margin-bottom: 30px;
}

.order-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.detail-section {
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 5px;
}

.detail-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
  color: #333;
}

.detail-section p {
  margin: 8px 0;
  font-size: 14px;
  color: #555;
}

.order-items {
  margin-bottom: 30px;
}

.detail-items-list {
  margin-top: 15px;
}

.detail-item {
  display: flex;
  padding: 15px;
  border-bottom: 1px solid #eee;
  align-items: center;
}

.detail-item-image {
  width: 70px;
  height: 70px;
  overflow: hidden;
  border-radius: 5px;
  margin-right: 15px;
}

.detail-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.detail-item-info {
  flex: 1;
}

.detail-item-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
}

.detail-item-info p {
  margin: 5px 0;
  font-size: 14px;
  color: #666;
}

.detail-item-total {
  font-weight: bold;
  font-size: 16px;
}

.order-timeline {
  margin-bottom: 30px;
}

.timeline {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  position: relative;
}

.timeline::before {
  content: '';
  position: absolute;
  top: 15px;
  left: 10px;
  right: 10px;
  height: 2px;
  background-color: #ddd;
  z-index: 1;
}

.timeline-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
  text-align: center;
  z-index: 2;
}

.timeline-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #ddd;
  margin-bottom: 10px;
}

.timeline-item.active .timeline-icon {
  background-color: #27ae60;
}

.timeline-content h4 {
  margin: 0 0 5px 0;
  font-size: 14px;
  color: #333;
}

.timeline-content p {
  margin: 0;
  font-size: 12px;
  color: #666;
}

.order-detail-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 30px;
}

.btn-close-detail {
  padding: 10px 20px;
  background-color: #f1f1f1;
  color: #333;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s;
}

.btn-close-detail:hover {
  background-color: #e0e0e0;
}

/* Responsive */
@media (max-width: 768px) {
  .checkout-content {
    width: 95%;
    max-width: none;
    padding: 15px;
  }
  
  .step-text {
    font-size: 12px;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .order-info {
    grid-template-columns: 1fr;
  }
  
  .order-detail-content {
    width: 95%;
    max-width: none;
    padding: 15px;
  }
  
  .timeline {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .timeline::before {
    top: 0;
    bottom: 0;
    left: 15px;
    right: auto;
    width: 2px;
    height: auto;
  }
  
  .timeline-item {
    flex-direction: row;
    width: 100%;
    margin-bottom: 15px;
  }
  
  .timeline-icon {
    margin-right: 15px;
    margin-bottom: 0;
  }
  
  .timeline-content {
    text-align: left;
  }
}