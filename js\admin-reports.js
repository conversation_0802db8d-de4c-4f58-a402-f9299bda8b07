/**
 * Qu<PERSON>n lý báo cáo cho Admin Dashboard
 * - T<PERSON><PERSON> và xuất báo cáo do<PERSON>h số bán hàng
 * - T<PERSON><PERSON> báo cáo tồn kho và sản phẩm
 * - Tạo báo cáo khách hàng và hành vi mua hàng
 * - Hỗ trợ tạo báo cáo tùy chỉnh
 */

// Danh sách các báo cáo đã tạo
let generatedReports = [];

document.addEventListener('DOMContentLoaded', function() {
  // Khởi tạo chức năng báo cáo
  initReports();
  
  // Thiết lập các nút tạo báo cáo
  setupReportButtons();
  
  // Nút làm mới báo cáo
  const refreshReportsBtn = document.getElementById('refresh-reports-btn');
  if (refreshReportsBtn) {
    refreshReportsBtn.addEventListener('click', function() {
      loadRecentReports();
      showNotification('Đã cập nhật danh sách báo cáo', 'success');
    });
  }
});

/**
 * Khởi tạo hệ thống báo cáo
 */
function initReports() {
  // Tải danh sách báo cáo gần đây
  loadRecentReports();
}

/**
 * Thiết lập các nút tạo báo cáo
 */
function setupReportButtons() {
  // Nút tạo báo cáo nhanh
  const generateButtons = document.querySelectorAll('.generate-report');
  generateButtons.forEach(button => {
    button.addEventListener('click', function() {
      const reportType = this.getAttribute('data-report');
      generateReport(reportType);
    });
  });
  
  // Nút tạo báo cáo tùy chỉnh
  const customReportBtn = document.getElementById('create-custom-report');
  if (customReportBtn) {
    customReportBtn.addEventListener('click', function() {
      openCustomReportModal();
    });
  }
}

/**
 * Tải danh sách báo cáo gần đây
 */
function loadRecentReports() {
  // Thử tải từ API
  fetch('http://localhost:3000/reports')
    .then(response => {
      if (!response.ok) {
        throw new Error('Không thể kết nối đến API');
      }
      return response.json();
    })
    .then(reports => {
      generatedReports = reports;
      displayRecentReports(reports);
      
      // Lưu vào cache
      localStorage.setItem('reportsCache', JSON.stringify(reports));
    })
    .catch(error => {
      console.error('Lỗi khi tải báo cáo:', error);
      
      // Fallback: Sử dụng cache
      const cachedReports = JSON.parse(localStorage.getItem('reportsCache')) || [];
      generatedReports = cachedReports;
      displayRecentReports(cachedReports);
    });
}

/**
 * Hiển thị danh sách báo cáo gần đây
 */
function displayRecentReports(reports) {
  const reportsTableBody = document.getElementById('recent-reports-tbody');
  if (!reportsTableBody) return;
  
  if (!reports || reports.length === 0) {
    reportsTableBody.innerHTML = `
      <tr>
        <td colspan="5" class="text-center">Chưa có báo cáo nào được tạo</td>
      </tr>
    `;
    return;
  }
  
  // Sắp xếp báo cáo theo thời gian (mới nhất lên đầu)
  reports.sort((a, b) => new Date(b.createdAt || 0) - new Date(a.createdAt || 0));
  
  // Chỉ hiển thị 10 báo cáo gần nhất
  const recentReports = reports.slice(0, 10);
  
  let html = '';
  
  recentReports.forEach(report => {
    // Format thông tin
    const reportDate = new Date(report.createdAt || new Date());
    const formattedDate = formatDate(reportDate);
    
    // Loại báo cáo
    let reportTypeName = 'Tùy chỉnh';
    let reportTypeClass = 'custom';
    
    switch (report.type) {
      case 'sales':
        reportTypeName = 'Doanh số';
        reportTypeClass = 'sales';
        break;
      case 'inventory':
        reportTypeName = 'Tồn kho';
        reportTypeClass = 'inventory';
        break;
      case 'customers':
        reportTypeName = 'Khách hàng';
        reportTypeClass = 'customers';
        break;
    }
    
    html += `
      <tr data-id="${report.id}">
        <td>${report.name || 'Báo cáo không tên'}</td>
        <td><span class="report-type ${reportTypeClass}">${reportTypeName}</span></td>
        <td>${formattedDate}</td>
        <td>${report.createdBy || 'Admin'}</td>
        <td>
          <button class="report-action-btn view" title="Xem báo cáo" onclick="viewReport('${report.id}')">
            <i class="fas fa-eye"></i>
          </button>
          <button class="report-action-btn download" title="Tải về" onclick="downloadReport('${report.id}')">
            <i class="fas fa-download"></i>
          </button>
          <button class="report-action-btn delete" title="Xóa báo cáo" onclick="deleteReport('${report.id}')">
            <i class="fas fa-trash-alt"></i>
          </button>
        </td>
      </tr>
    `;
  });
  
  reportsTableBody.innerHTML = html;
}

/**
 * Tạo báo cáo theo loại
 */
function generateReport(type) {
  // Hiển thị thông báo đang tạo báo cáo
  showNotification('Đang tạo báo cáo...', 'info');
  
  // Lấy dữ liệu cần thiết cho báo cáo
  Promise.all([
    getAllOrdersData(),
    getAllProductsData(),
    getAllUsersData()
  ])
    .then(([orders, products, users]) => {
      // Tên báo cáo
      let reportName;
      const today = new Date();
      const dateStr = today.toLocaleDateString('vi-VN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      }).replace(/\//g, '-');
      
      // Dữ liệu báo cáo
      let reportData;
      
      switch (type) {
        case 'sales':
          reportName = `Báo cáo doanh số ${dateStr}`;
          reportData = generateSalesReportData(orders);
          break;
        case 'inventory':
          reportName = `Báo cáo tồn kho ${dateStr}`;
          reportData = generateInventoryReportData(products);
          break;
        case 'customers':
          reportName = `Báo cáo khách hàng ${dateStr}`;
          reportData = generateCustomerReportData(users, orders);
          break;
        default:
          throw new Error('Loại báo cáo không hợp lệ');
      }
      
      // Tạo báo cáo
      const report = {
        id: generateId(),
        name: reportName,
        type: type,
        createdAt: new Date().toISOString(),
        createdBy: 'Admin',
        data: reportData
      };
      
      // Lưu vào API
      return fetch('http://localhost:3000/reports', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(report)
      })
        .then(response => {
          if (!response.ok) {
            throw new Error('Không thể lưu báo cáo');
          }
          return response.json();
        })
        .then(savedReport => {
          // Cập nhật giao diện
          generatedReports.unshift(savedReport);
          displayRecentReports(generatedReports);
          
          // Cập nhật cache
          localStorage.setItem('reportsCache', JSON.stringify(generatedReports));
          
          showNotification(`Đã tạo báo cáo ${reportName}`, 'success');
          
          // Hiển thị báo cáo
          viewReport(savedReport.id);
          
          return savedReport;
        });
    })
    .catch(error => {
      console.error('Lỗi khi tạo báo cáo:', error);
      showNotification('Không thể tạo báo cáo: ' + error.message, 'error');
    });
}

/**
 * Tạo dữ liệu báo cáo doanh số
 */
function generateSalesReportData(orders) {
  // Lọc các đơn hàng đã hoàn thành
  const completedOrders = orders.filter(order => 
    order.orderStatus === 'delivered' || 
    order.orderStatus === 'completed'
  );
  
  // Tính tổng doanh thu
  const totalRevenue = completedOrders.reduce((sum, order) => sum + (order.total || 0), 0);
  
  // Doanh thu theo tháng
  const monthlyRevenue = {};
  const last6Months = [];
  
  // Lấy 6 tháng gần nhất
  const today = new Date();
  for (let i = 5; i >= 0; i--) {
    const month = new Date(today.getFullYear(), today.getMonth() - i, 1);
    const monthKey = month.toISOString().substring(0, 7); // YYYY-MM
    monthlyRevenue[monthKey] = 0;
    last6Months.push(monthKey);
  }
  
  // Tính doanh thu theo tháng
  completedOrders.forEach(order => {
    const orderDate = new Date(order.orderDate || order.createdAt || 0);
    const monthKey = orderDate.toISOString().substring(0, 7); // YYYY-MM
    
    if (monthlyRevenue.hasOwnProperty(monthKey)) {
      monthlyRevenue[monthKey] += (order.total || 0);
    }
  });
  
  // Sản phẩm bán chạy
  const productSales = {};
  
  completedOrders.forEach(order => {
    if (order.items && Array.isArray(order.items)) {
      order.items.forEach(item => {
        const productName = item.productName || item.name || 'Sản phẩm không xác định';
        const quantity = item.quantity || 1;
        
        if (!productSales[productName]) {
          productSales[productName] = {
            quantity: 0,
            revenue: 0
          };
        }
        
        productSales[productName].quantity += quantity;
        productSales[productName].revenue += (item.price * quantity) || 0;
      });
    }
  });
  
  // Sắp xếp sản phẩm bán chạy theo doanh thu
  const topProducts = Object.entries(productSales)
    .map(([name, data]) => ({ name, ...data }))
    .sort((a, b) => b.revenue - a.revenue)
    .slice(0, 10);
  
  return {
    summary: {
      totalRevenue,
      totalOrders: completedOrders.length,
      averageOrderValue: completedOrders.length ? totalRevenue / completedOrders.length : 0
    },
    monthlyRevenue: last6Months.map(month => ({
      month,
      revenue: monthlyRevenue[month]
    })),
    topProducts
  };
}

/**
 * Tạo dữ liệu báo cáo tồn kho
 */
function generateInventoryReportData(products) {
  // Tổng số sản phẩm
  const totalProducts = products.length;
  
  // Số lượng sản phẩm theo danh mục
  const categoryCounts = {};
  
  products.forEach(product => {
    const category = product.category || 'Không phân loại';
    
    if (!categoryCounts[category]) {
      categoryCounts[category] = 0;
    }
    
    categoryCounts[category]++;
  });
  
  // Sản phẩm hết hàng hoặc sắp hết hàng
  const lowStockThreshold = 5;
  const lowStockProducts = products
    .filter(product => {
      const stock = parseInt(product.stock || '0', 10);
      return stock <= lowStockThreshold && stock > 0;
    })
    .map(product => ({
      id: product.id,
      name: product.name,
      stock: parseInt(product.stock || '0', 10),
      price: product.price
    }));
  
  const outOfStockProducts = products
    .filter(product => {
      const stock = parseInt(product.stock || '0', 10);
      return stock === 0;
    })
    .map(product => ({
      id: product.id,
      name: product.name,
      price: product.price
    }));
  
  return {
    summary: {
      totalProducts,
      lowStockCount: lowStockProducts.length,
      outOfStockCount: outOfStockProducts.length
    },
    categoryDistribution: Object.entries(categoryCounts).map(([category, count]) => ({ 
      category, 
      count,
      percentage: (count / totalProducts * 100).toFixed(2)
    })),
    lowStockProducts,
    outOfStockProducts
  };
}

/**
 * Tạo dữ liệu báo cáo khách hàng
 */
function generateCustomerReportData(users, orders) {
  // Tổng số khách hàng
  const totalUsers = users.length;
  
  // Khách hàng mới trong tháng này
  const currentDate = new Date();
  const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
  
  const newUsers = users.filter(user => {
    const createdAt = new Date(user.createdAt || user.registeredDate || 0);
    return createdAt >= firstDayOfMonth;
  }).length;
  
  // Khách hàng theo đơn hàng
  const userOrders = {};
  
  orders.forEach(order => {
    const userId = order.userId;
    
    if (!userId) return;
    
    if (!userOrders[userId]) {
      userOrders[userId] = {
        count: 0,
        total: 0
      };
    }
    
    userOrders[userId].count++;
    userOrders[userId].total += (order.total || 0);
  });
  
  // Khách hàng có nhiều đơn hàng nhất
  const topCustomers = Object.entries(userOrders)
    .map(([userId, data]) => {
      const user = users.find(u => u.id === userId) || {};
      return {
        id: userId,
        name: user.fullName || user.username || 'Khách hàng',
        orderCount: data.count,
        totalSpent: data.total
      };
    })
    .sort((a, b) => b.totalSpent - a.totalSpent)
    .slice(0, 10);
  
  return {
    summary: {
      totalUsers,
      newUsersThisMonth: newUsers,
      activeUsers: Object.keys(userOrders).length
    },
    topCustomers
  };
}

/**
 * Mở modal tạo báo cáo tùy chỉnh
 */
function openCustomReportModal() {
  alert('Chức năng tạo báo cáo tùy chỉnh đang được phát triển. Vui lòng thử lại sau.');
}

/**
 * Xem báo cáo
 */
function viewReport(id) {
  const report = generatedReports.find(r => r.id === id);
  
  if (!report) {
    showNotification('Không tìm thấy báo cáo', 'error');
    return;
  }
  
  alert(`
    Báo cáo: ${report.name}
    Loại: ${report.type}
    Ngày tạo: ${formatDate(new Date(report.createdAt))}
    
    Chi tiết báo cáo đã được mở trong tab mới (giả lập).
  `);
}

/**
 * Tải báo cáo về máy
 */
function downloadReport(id) {
  const report = generatedReports.find(r => r.id === id);
  
  if (!report) {
    showNotification('Không tìm thấy báo cáo', 'error');
    return;
  }
  
  // Giả lập tải về
  showNotification('Đang tải báo cáo xuống...', 'info');
  
  setTimeout(() => {
    showNotification(`Đã tải báo cáo "${report.name}" thành công`, 'success');
  }, 1500);
}

/**
 * Xóa báo cáo
 */
function deleteReport(id) {
  if (!confirm('Bạn có chắc chắn muốn xóa báo cáo này?')) {
    return;
  }
  
  // Xóa trên API
  fetch(`http://localhost:3000/reports/${id}`, {
    method: 'DELETE'
  })
    .then(response => {
      if (!response.ok) {
        throw new Error('Không thể xóa báo cáo');
      }
      return response.json();
    })
    .then(() => {
      // Xóa khỏi danh sách
      generatedReports = generatedReports.filter(r => r.id !== id);
      
      // Cập nhật UI
      displayRecentReports(generatedReports);
      
      // Cập nhật cache
      localStorage.setItem('reportsCache', JSON.stringify(generatedReports));
      
      showNotification('Đã xóa báo cáo', 'success');
    })
    .catch(error => {
      console.error('Lỗi khi xóa báo cáo:', error);
      
      // Fallback: Xóa trong local
      generatedReports = generatedReports.filter(r => r.id !== id);
      
      // Cập nhật UI
      displayRecentReports(generatedReports);
      
      // Cập nhật cache
      localStorage.setItem('reportsCache', JSON.stringify(generatedReports));
      
      showNotification('Đã xóa báo cáo (chế độ ngoại tuyến)', 'warning');
    });
}

/**
 * Lấy dữ liệu đơn hàng từ tất cả các nguồn
 */
function getAllOrdersData() {
  return new Promise((resolve, reject) => {
    // Thử tải từ API
    fetch('http://localhost:3000/orders')
      .then(response => {
        if (!response.ok) {
          throw new Error('Không thể kết nối đến API');
        }
        return response.json();
      })
      .then(apiOrders => {
        // Lấy dữ liệu từ localStorage
        const localOrders = JSON.parse(localStorage.getItem('orders')) || [];
        const orderHistory = JSON.parse(localStorage.getItem('orderHistory')) || [];
        const cachedOrders = JSON.parse(localStorage.getItem('allOrdersCache')) || [];
        
        // Kết hợp tất cả các nguồn dữ liệu
        const allOrdersMap = new Map();
        
        [...apiOrders, ...cachedOrders, ...localOrders, ...orderHistory].forEach(order => {
          allOrdersMap.set(order.id, order);
        });
        
        const allOrders = Array.from(allOrdersMap.values());
        resolve(allOrders);
      })
      .catch(error => {
        console.error('Lỗi khi tải đơn hàng từ API:', error);
        
        // Dùng dữ liệu từ localStorage
        const localOrders = JSON.parse(localStorage.getItem('orders')) || [];
        const orderHistory = JSON.parse(localStorage.getItem('orderHistory')) || [];
        const cachedOrders = JSON.parse(localStorage.getItem('allOrdersCache')) || [];
        
        // Kết hợp tất cả các nguồn dữ liệu
        const allOrdersMap = new Map();
        
        [...cachedOrders, ...localOrders, ...orderHistory].forEach(order => {
          allOrdersMap.set(order.id, order);
        });
        
        const allOrders = Array.from(allOrdersMap.values());
        resolve(allOrders);
      });
  });
}

/**
 * Lấy dữ liệu sản phẩm
 */
function getAllProductsData() {
  return new Promise((resolve, reject) => {
    // Lấy từ localStorage
    const products = JSON.parse(localStorage.getItem('products')) || [];
    resolve(products);
  });
}

/**
 * Lấy dữ liệu người dùng
 */
function getAllUsersData() {
  return new Promise((resolve, reject) => {
    // Lấy từ localStorage
    const users = JSON.parse(localStorage.getItem('users')) || [];
    resolve(users);
  });
}

/**
 * Tạo ID ngẫu nhiên
 */
function generateId() {
  return 'report_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
}

/**
 * Định dạng ngày tháng
 */
function formatDate(date) {
  return new Date(date).toLocaleDateString('vi-VN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}