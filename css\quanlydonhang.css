/* Styles cho quản lý đơn hàng */

/* Section chính */
#order-management {
  padding: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h2 {
  font-size: 24px;
  color: #333;
  margin: 0;
}

.section-actions {
  display: flex;
  gap: 15px;
}

.search-box {
  position: relative;
}

.search-box input {
  padding: 10px 15px 10px 35px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  width: 250px;
}

.search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #777;
}

/* Filter tabs */
.filter-tabs {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  overflow-x: auto;
  white-space: nowrap;
  padding-bottom: 1px;
}

.filter-tab {
  padding: 12px 20px;
  cursor: pointer;
  position: relative;
  color: #555;
  font-weight: 500;
}

.filter-tab.active {
  color: var(--primary-color, #FB9400);
  font-weight: bold;
}

.filter-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--primary-color, #FB9400);
}

.order-count {
  display: inline-block;
  padding: 2px 6px;
  background-color: #f1f1f1;
  border-radius: 10px;
  font-size: 12px;
  color: #555;
  margin-left: 5px;
}

.filter-tab.active .order-count {
  background-color: var(--primary-color, #FB9400);
  color: white;
}

/* Table */
.table-responsive {
  overflow-x: auto;
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
}

.admin-table th {
  background-color: #f9f9f9;
  padding: 12px 15px;
  text-align: left;
  font-weight: bold;
  color: #333;
  white-space: nowrap;
}

.admin-table td {
  padding: 12px 15px;
  border-top: 1px solid #eee;
  vertical-align: middle;
}

.admin-table tbody tr:hover {
  background-color: #f9f9f9;
}

/* Order status */
.order-status {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: bold;
  text-align: center;
}

.status-processing {
  background-color: #f39c12;
  color: white;
}

.status-confirmed {
  background-color: #3498db;
  color: white;
}

.status-shipping {
  background-color: #9b59b6;
  color: white;
}

.status-delivered {
  background-color: #2ecc71;
  color: white;
}

.status-completed {
  background-color: #27ae60;
  color: white;
}

.status-cancelled {
  background-color: #e74c3c;
  color: white;
}

/* Status select */
.order-status-select {
  padding: 8px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 14px;
  width: 100%;
  max-width: 150px;
}

/* Order row colors */
.order-processing {
  background-color: rgba(243, 156, 18, 0.05);
}

.order-completed {
  background-color: rgba(39, 174, 96, 0.05);
}

.order-cancelled {
  background-color: rgba(231, 76, 60, 0.05);
}

/* Action buttons */
.order-actions {
  display: flex;
  gap: 10px;
}

.btn-view-order,
.btn-delete-order {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  padding: 5px;
  border-radius: 3px;
  transition: background-color 0.3s;
}

.btn-view-order {
  color: var(--primary-color, #FB9400);
}

.btn-delete-order {
  color: #e74c3c;
}

.btn-view-order:hover {
  background-color: rgba(251, 148, 0, 0.1);
}

.btn-delete-order:hover {
  background-color: rgba(231, 76, 60, 0.1);
}

/* Loading */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid var(--primary-color, #FB9400);
  border-radius: 50%;
  margin: 20px auto;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Detail Modal */
.admin-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.admin-modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  border-radius: 5px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  overflow-y: auto;
  padding: 20px;
}

.admin-modal-content .close-btn {
  position: absolute;
  top: 15px;
  right: 20px;
  font-size: 24px;
  cursor: pointer;
  color: #777;
}

.modal-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.modal-header h2 {
  margin: 0;
  color: #333;
}

.modal-body {
  margin-bottom: 20px;
}

.order-detail-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.info-section {
  background-color: #f9f9f9;
  border-radius: 5px;
  padding: 15px;
}

.info-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
  color: #333;
}

.info-table {
  width: 100%;
}

.info-table td {
  padding: 5px 0;
  line-height: 1.4;
}

.info-table td:first-child {
  font-weight: bold;
  color: #555;
  width: 40%;
}

.order-items-section {
  margin-bottom: 30px;
}

.order-items-section h3 {
  margin-bottom: 15px;
  font-size: 18px;
  color: #333;
}

.items-table {
  width: 100%;
  border-collapse: collapse;
}

.items-table th {
  background-color: #f9f9f9;
  padding: 10px;
  text-align: left;
  font-weight: bold;
  color: #333;
}

.items-table td {
  padding: 10px;
  border-top: 1px solid #eee;
}

.item-info {
  display: flex;
  align-items: center;
}

.item-image {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: 3px;
  margin-right: 10px;
}

.item-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.item-note {
  font-size: 12px;
  color: #777;
}

.text-right {
  text-align: right;
}

.order-total td {
  font-weight: bold;
  font-size: 16px;
}

.order-timeline {
  margin-bottom: 30px;
}

.order-timeline h3 {
  margin-bottom: 15px;
  font-size: 18px;
  color: #333;
}

.timeline {
  position: relative;
  padding-left: 30px;
}

.timeline::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 10px;
  width: 2px;
  background-color: #eee;
}

.timeline-item {
  position: relative;
  margin-bottom: 20px;
  padding-bottom: 20px;
}

.timeline-icon {
  position: absolute;
  top: 0;
  left: -20px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #ddd;
  z-index: 1;
}

.timeline-content {
  padding-left: 10px;
}

.timeline-time {
  font-size: 12px;
  color: #777;
  margin-bottom: 5px;
}

.timeline-text {
  font-weight: bold;
  color: #333;
}

.modal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.status-update {
  display: flex;
  gap: 10px;
}

#admin-status-select {
  padding: 8px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

#btn-update-status {
  background-color: var(--primary-color, #FB9400);
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
}

#btn-update-status:hover {
  background-color: #e08500;
}

.btn-close {
  background-color: #f1f1f1;
  color: #333;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
}

.btn-close:hover {
  background-color: #e0e0e0;
}

/* Notification */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: white;
  border-left: 4px solid #2ecc71;
  border-radius: 4px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
  z-index: 1010;
  max-width: 350px;
  transform: translateX(150%);
  transition: transform 0.3s ease-in-out;
}

.notification.show {
  transform: translateX(0);
}

.notification.success {
  border-left-color: #2ecc71;
}

.notification.error {
  border-left-color: #e74c3c;
}

.notification.warning {
  border-left-color: #f39c12;
}

.notification.info {
  border-left-color: #3498db;
}

.notification-content {
  padding: 15px;
  padding-right: 40px;
}

.notification-close {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 18px;
  cursor: pointer;
  color: #777;
}

/* Responsive */
@media (max-width: 768px) {
  .order-detail-info {
    grid-template-columns: 1fr;
  }
  
  .admin-modal-content {
    width: 95%;
    padding: 15px;
  }
  
  .items-table {
    display: block;
    overflow-x: auto;
  }
  
  .modal-footer {
    flex-direction: column;
    gap: 15px;
  }
  
  .status-update {
    width: 100%;
  }
  
  .btn-close {
    width: 100%;
  }
}