<!DOCTYPE html>
<html lang="vi" class="html">
<head class="head">
    <meta charset="UTF-8" class="meta-charset">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" class="meta-viewport">
    <title class="title">Na Food</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <link href="https://fonts.googleapis.com/css2?family=Lobster&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css" rel="stylesheet" class="fontawesome-link-1">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" class="fontawesome-link-2">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">

    <link rel="stylesheet" href="./acsets/css/styles.css" class="main-style">
    <link rel="stylesheet" href="./acsets/css/taikhoan.css" class="main-style">
    <link rel="stylesheet" href="./acsets/css/giohang.css" class="main-style">

</head>
<body class="body">
    <div class="nafood-container">
         <header class="header">
            <div class="logo">
                <img src="./acsets/image/logo_food.png" alt="Na Food Logo" class="logo-img">
                <span class="logo-text">NA FOOD</span>
            </div>
            <form action="" class="form-search" onsubmit="return false;">
                <i class="fas fa-search search-icon"></i>
                <input type="text" id="searchInput" class="form-search-input" placeholder="Tìm kiếm món ăn...">
                <button type="button" class="filter-btn" onclick="toggleFilter()">Lọc</button>
            </form>
            
            
            <div class="header-middle-right">
                <li class="header-middle-right-item dropdown open">
                    <i class="fa-solid fa-user"></i>
                    <div class="auth-container">
                        <span class="text-dndk">Đăng nhập / Đăng ký</span>
                        <span class="text-tk">Tài khoản <i class="fa-solid fa-caret-down"></i></span>
                    </div> 
                    <!-- Phần dropdown sẽ hiển thị khi hover -->
                    <div class="account-dropdown-wrapper">
                        <ul class="account-dropdown-list">
                            <li class="account-dropdown-item" onclick="openLoginModal()">
                                <i class="fa-solid fa-right-to-bracket"></i> Đăng nhập
                            </li>
                            <li class="account-dropdown-item" onclick="openRegisterModal()">
                                <i class="fa-solid fa-user-plus"></i> Đăng ký
                            </li>
                        </ul>
                    </div>
                </li>
                <!-- Modal đăng nhập -->
                <div class="modal modal-login" id="loginModal">
                    <div class="modal-content">
                        <span class="modal-close" onclick="closeModal('loginModal')">&times;</span>
                        <h2 class="modal-title">Đăng nhập tài khoản</h2>
                        <p class="modal-description">Đăng nhập thành viên để mua hàng và nhận những ưu đãi đặc biệt từ chúng tôi</p>
                        <input type="text" class="modal-input" placeholder="Nhập số điện thoại">
                        <input type="password" class="modal-input" placeholder="Nhập mật khẩu">
                        <button class="modal-button btn-login">ĐĂNG NHẬP</button>
                        <p class="modal-switch-text">
                            Bạn chưa có tài khoản ? 
                            <a href="#" class="modal-switch-link" onclick="switchToRegister()">Đăng kí ngay</a>
                        </p>
                    </div>
                </div>
                <!-- Modal đăng ký -->
           <!-- HTML (giữ nguyên phần modal đăng ký như bạn đã có) -->
            <div class="modal modal-register" id="registerModal">
                <div class="modal-content">
                    <span class="modal-close" onclick="closeModal('registerModal')">&times;</span>
                    <h2 class="modal-title">Đăng ký tài khoản</h2>
                    <p class="modal-description">Đăng ký thành viên để mua hàng và nhận những ưu đãi đặc biệt từ chúng tôi</p>
                    <input type="text" class="modal-input" id="nameInput" placeholder="VD: Van Na">
                    <input type="text" class="modal-input" id="phoneInput" placeholder="Nhập số điện thoại">
                    <input type="password" class="modal-input" id="passwordInput" placeholder="Nhập mật khẩu">
                    <input type="password" class="modal-input" id="confirmPasswordInput" placeholder="Nhập lại mật khẩu">
                    <label class="modal-checkbox-wrapper">
                        <input type="checkbox" class="modal-checkbox" id="policyCheckbox"> Tôi đồng ý với 
                        <a href="#" class="modal-policy-link">chính sách trang web</a>
                    </label>
                    <button class="modal-button btn-register" id="registerBtn">ĐĂNG KÝ</button>
                    <p class="modal-switch-text">
                        Đã có tài khoản ? 
                        <a href="#" class="modal-switch-link" onclick="switchToLogin()">Đăng nhập ngay</a>
                    </p>
                </div>

            </div>
                 <div class="cart">
                    <button class="cart-btn">
                        <i class="fas fa-shopping-cart"></i>
                        Giỏ hàng
                        <span class="cart-count" id="cart-count">0</span> <!-- Số lượng -->
                    </button>
                </div>
            </div>
            <div class="cart-overlay" onclick="closeCart()"></div>

            <div class="cart-container">
              <div class="cart-header">
                <h2 class="cart-title">🧺 Giỏ hàng</h2>
                <button class="close-btn cart-close-btn" onclick="closeCart()">✕</button>
              </div>
            
              <div id="cartContent">
                <div class="cart-empty">
                  <i class="cart-empty-icon"></i>
                  <p>Không có sản phẩm nào trong giỏ hàng của bạn</p>
                </div>
              </div>
              
            
              <div class="cart-footer cart-section">
                <div class="total cart-total">
                  <span class="total-label">Tổng tiền:</span>
                  <span class="amount cart-total-amount" id="cartTotal">0 ₫</span>
                </div>
                <div class="buttons cart-buttons">
                  <button class="add-more cart-add-more" onclick="closeCart()">+ Thêm món</button>
                  <button class="checkout cart-checkout" onclick="checkoutCart()">Thanh toán</button>
                </div>
              </div>
            </div>
            
        </header>
        <div class="filter-bar hidden">
            <label for="category" class="filter-label">Phân loại</label>
            <select id="category" class="filter-select">
              <option>Tất cả</option>
              <option>Món chay</option>
              <option>Món mặn</option>
              <option>Món lẩu</option>
              <option>Món ăn vặt</option>
              <option>Nước uống</option>
              
            </select>
          
            <label for="price-min" class="filter-label">Giá từ</label>
            <input type="number" id="price-min" placeholder="tối thiểu" class="filter-input">
          
            <label for="price-max" class="filter-label">đến</label>
            <input type="number" id="price-max" placeholder="tối đa" class="filter-input">
          
            <button class="search-btn"><i class="fas fa-search-dollar"></i></button>
            <button class="sort-btn"><i class="fas fa-sort-amount-up"></i></button>
            <button class="sort-btn"><i class="fas fa-sort-amount-down"></i></button>
            <button class="refresh-btn"><i class="fas fa-sync-alt"></i></button>
            <button class="close-btn"><i class="fas fa-times"></i></button>
          </div>
          

        <nav class="nav">
            <a href="#" class="nav-link">TRANG CHỦ</a>
            <a href="#" class="nav-link">MÓN CHAY</a>
            <a href="#" class="nav-link">MÓN MẶN</a>
            <a href="#" class="nav-link">MÓN LẨU</a>
            <a href="#" class="nav-link">MÓN ĂN VẶT</a>
            <a href="#" class="nav-link">NƯỚC UỐNG</a>
            <a href="#" class="nav-link">MÓN KHÁC</a>
        </nav>

        <section class="banner">
            <div class="banner-content">
                <h1 class="banner-title">NA FOOD</h1>
                <p class="banner-subtitle">"Tinh hoa ẩm thực"</p>
                <button class="order-btn">Đặt hàng ngay</button>
            </div>
        </section>

        <section class="services">
            <div class="service-item">
                <i class="fas fa-shipping-fast service-icon"></i>
                <div class="service-text">
                    <h3 class="service-title">GIAO HÀNG NHANH</h3>
                    <p class="service-description">Cho tất cả đơn hàng</p>
                </div>
            </div>
            <div class="service-item">
                <i class="fas fa-heart service-icon"></i>
                <div class="service-text">
                    <h3 class="service-title">SẢN PHẨM AN TOÀN</h3>
                    <p class="service-description">Cam kết chất lượng</p>
                </div>
            </div>
            <div class="service-item">
                <i class="fas fa-headset service-icon"></i>
                <div class="service-text">
                    <h3 class="service-title">HỖ TRỢ 24/7</h3>
                    <p class="service-description">Tất cả ngày trong tuần</p>
                </div>
            </div>
            <div class="service-item">
                <i class="fas fa-dollar-sign service-icon"></i>
                <div class="service-text">
                    <h3 class="service-title">HOÀN LẠI TIỀN</h3>
                    <p class="service-description">Nếu không hài lòng</p>
                </div>
            </div>
        </section>

        <section class="menu-heading">
            <h2 class="menu-title">KHÁM PHÁ THỰC ĐƠN CỦA CHÚNG TÔI</h2>
            <div class="underline"></div>
        </section>

        <!-- Danh sách sản phẩm -->



        
            <!-- Danh sách sản phẩm -->
                <div class="home-products" id="home-products"></div>

                <!-- Thanh phân trang -->
                <ul class="page-nav-list" id="page-nav-list"></ul>


            <!-- Popup chi tiết sản phẩm -->
            <div id="productModal" class="modal" style="display: none;">
            <div class="modal-content">
                <span class="close">&times;</span>
                <img id="modalImage" src="" alt="Hình món ăn" style="width: 100%; border-radius: 8px;" />
                <h2 id="modalName"></h2>
                <p id="modalPrice" style="color: red;"></p>
                <p id="modalDesc"></p>
                <div>
                <button id="decrease">-</button>
                <span id="quantity">1</span>
                <button id="increase">+</button>
                </div>
                <textarea id="note" placeholder="Nhập thông tin cần lưu ý..."></textarea>
                <p>Thành tiền: <span id="modalTotal" style="color: red;"></span></p>
               
                <div class="action-buttons">
                    <button id="orderNow">Đặt hàng ngay</button>
                    <button id="cartIcon"><i class="fa fa-shopping-cart"></i></button>
                  </div>
                  
            </div>
            </div>



        <div class="page-nav">
            <ul class="page-nav-list">
            </ul>
        </div>

        <footer class="footer">
            <div class="footer-container">
                <div class="footer-logo">
                    <img src="image/logo_food.png" alt="NA FOOD" class="footer-logo-img">
                    <span class="logo-text">NA FOOD</span>
                </div>
                <div class="footer-subscribe">
                    <div class="subscribe-text-container">
                        <h3 class="subscribe-title">ĐĂNG KÝ NHẬN TIN</h3>
                        <p class="subscribe-text">Nhận thông tin mới nhất từ chúng tôi</p>
                    </div>
                    <div class="subscribe-form">
                        <input type="email" class="subscribe-input" placeholder="Nhập email của bạn">
                        <button class="subscribe-button">ĐĂNG KÝ</button>
                    </div>
                </div>        
            </div>

            <div class="footer-links">
                <div class="footer-about">
                    <h4 class="footer-title">VỀ CHÚNG TÔI</h4>
                    <p class="footer-description">NA Food là thương hiệu được thành lập vào năm 2022 với tiêu chí đặt chất lượng sản phẩm lên hàng đầu.</p>
                    <div class="social-icons">
                        <a href="#" class="social-icon"><i class="fa-brands fa-facebook"></i></a>
                        <a href="#" class="social-icon"><i class="fa-brands fa-twitter"></i></a>
                        <a href="#" class="social-icon"><i class="fa-brands fa-linkedin"></i></a>
                        <a href="#" class="social-icon"><i class="fa-brands fa-whatsapp"></i></a>
                    </div>
                </div>
                <div class="footer-menu">
                    <h4 class="footer-title">LIÊN KẾT</h4>
                    <ul class="footer-list">
                        <li><a href="#" class="footer-link">Về chúng tôi</a></li>
                        <li><a href="#" class="footer-link">Thực đơn</a></li>
                        <li><a href="#" class="footer-link">Điều khoản</a></li>
                        <li><a href="#" class="footer-link">Liên hệ</a></li>
                        <li><a href="#" class="footer-link">Tin tức</a></li>
                    </ul>
                </div>
                <div class="footer-menu">
                    <h4 class="footer-title">THỰC ĐƠN</h4>
                    <ul class="footer-list">
                        <li><a href="#" class="footer-link">Điểm tâm</a></li>
                        <li><a href="#" class="footer-link">Món chay</a></li>
                        <li><a href="#" class="footer-link">Món mặn</a></li>
                        <li><a href="#" class="footer-link">Nước uống</a></li>
                        <li><a href="#" class="footer-link">Tráng miệng</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h4 class="footer-title">LIÊN HỆ</h4>
                    <p class="contact-info"><i class="fa-solid fa-location-dot"></i> Phường 9, TP Trà Vinh, Tĩnh Trà Vinh</p>
                    <p class="contact-info"><i class="fa-solid fa-phone"></i> 0123 456 789 - 0333 661 121</p>
                    <p class="contact-info"><i class="fa-solid fa-envelope"></i> <EMAIL> - <EMAIL></p>
                </div>
            </div>

            <div class="footer-bottom">
                <p class="footer-bottom-text">Copyright 2022 © Na Food. All Rights Reserved.</p>
            </div>
        </footer>


       
            

          
  
        <script src="./js/script.js" class="main-script"></script>
        <script src="./js/taikhoan.js" class="main-script"></script>
        <script src="./js/giohang.js" class="main-script"></script>
     
        <script src="./js/sanpham.js" class="main-script"></script>
</div>
</body>
</html>
