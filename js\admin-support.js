/**
 * Qu<PERSON>n lý hỗ trợ khách hàng cho Admin Dashboard
 * - Hi<PERSON>n thị danh sách yêu cầu hỗ trợ
 * - <PERSON><PERSON> lý phản hồi và cập nhật trạng thái
 * - <PERSON><PERSON><PERSON> kết với hệ thống chat và email
 */

// Lưu trữ yêu cầu hỗ trợ
let supportRequests = [];

document.addEventListener('DOMContentLoaded', function() {
  // Khởi tạo chức năng hỗ trợ
  initSupportManagement();
  
  // Thiết lập các bộ lọc
  setupSupportFilters();
  
  // Đăng ký lắng nghe sự kiện yêu cầu hỗ trợ mới
  document.addEventListener('newSupportRequest', function(event) {
    console.log('Yêu cầu hỗ trợ mới:', event.detail.request);
    loadSupportRequests();
  });
});

/**
 * Khởi tạo quản lý hỗ trợ
 */
function initSupportManagement() {
  // Tải danh sách yêu cầu hỗ trợ
  loadSupportRequests();
  
  // Thiết lập tìm kiếm yêu cầu hỗ trợ
  const supportSearch = document.getElementById('support-search');
  if (supportSearch) {
    supportSearch.addEventListener('input', function() {
      filterSupportRequests(this.value.trim().toLowerCase());
    });
  }
}

/**
 * Tải danh sách yêu cầu hỗ trợ
 */
function loadSupportRequests() {
  // Hiển thị trạng thái đang tải
  const supportTableBody = document.getElementById('support-table-body');
  if (supportTableBody) {
    supportTableBody.innerHTML = `
      <tr>
        <td colspan="6" class="text-center">
          <div class="loading-spinner"></div>
          <p>Đang tải danh sách yêu cầu hỗ trợ...</p>
        </td>
      </tr>
    `;
  }
  
  // Thử tải từ API
  fetch('http://localhost:3000/support')
    .then(response => {
      if (!response.ok) {
        throw new Error('Không thể kết nối đến API');
      }
      return response.json();
    })
    .then(apiRequests => {
      displaySupportRequests(apiRequests);
      supportRequests = apiRequests;
      // Lưu vào cache
      localStorage.setItem('supportRequestsCache', JSON.stringify(apiRequests));
    })
    .catch(error => {
      console.error('Lỗi khi tải yêu cầu hỗ trợ từ API:', error);
      
      // Fallback: Tải từ localStorage
      const cachedRequests = JSON.parse(localStorage.getItem('supportRequestsCache')) || [];
      
      // Hiển thị danh sách
      displaySupportRequests(cachedRequests);
      supportRequests = cachedRequests;
      
      // Thông báo
      showNotification('Đang hiển thị dữ liệu hỗ trợ ngoại tuyến', 'warning');
    });
}

/**
 * Hiển thị danh sách yêu cầu hỗ trợ
 */
function displaySupportRequests(requests) {
  const supportTableBody = document.getElementById('support-table-body');
  if (!supportTableBody) return;
  
  if (!requests || requests.length === 0) {
    supportTableBody.innerHTML = `
      <tr>
        <td colspan="6" class="text-center">Chưa có yêu cầu hỗ trợ nào</td>
      </tr>
    `;
    updateSupportCounts({ all: 0, new: 0, processing: 0, resolved: 0 });
    return;
  }
  
  // Sắp xếp yêu cầu theo thời gian (mới nhất lên đầu)
  requests.sort((a, b) => new Date(b.createdAt || 0) - new Date(a.createdAt || 0));
  
  let html = '';
  
  requests.forEach(request => {
    // Lấy trạng thái
    let statusText = 'Mới';
    let statusClass = 'new';
    
    switch (request.status) {
      case 'processing':
        statusText = 'Đang xử lý';
        statusClass = 'processing';
        break;
      case 'resolved':
        statusText = 'Đã giải quyết';
        statusClass = 'resolved';
        break;
    }
    
    // Format ngày
    const createdDate = new Date(request.createdAt || new Date());
    const formattedDate = formatDate(createdDate);
    
    html += `
      <tr data-id="${request.id}">
        <td>${request.id}</td>
        <td>${request.customerName || 'Khách hàng'}</td>
        <td class="support-subject">${request.subject || 'Không có tiêu đề'}</td>
        <td>${formattedDate}</td>
        <td><span class="support-status ${statusClass}">${statusText}</span></td>
        <td>
          <button class="support-action-btn view" title="Xem chi tiết" onclick="viewSupportRequest('${request.id}')">
            <i class="fas fa-eye"></i>
          </button>
          <button class="support-action-btn reply" title="Phản hồi" onclick="replySupportRequest('${request.id}')">
            <i class="fas fa-reply"></i>
          </button>
          ${request.status !== 'resolved' ? `
            <button class="support-action-btn resolve" title="Đánh dấu đã giải quyết" onclick="resolveSupportRequest('${request.id}')">
              <i class="fas fa-check-circle"></i>
            </button>
          ` : ''}
        </td>
      </tr>
    `;
  });
  
  supportTableBody.innerHTML = html;
  
  // Cập nhật số lượng
  updateSupportCounts({
    all: requests.length,
    new: requests.filter(r => !r.status || r.status === 'new').length,
    processing: requests.filter(r => r.status === 'processing').length,
    resolved: requests.filter(r => r.status === 'resolved').length
  });
}

/**
 * Thiết lập bộ lọc yêu cầu hỗ trợ
 */
function setupSupportFilters() {
  const filterTabs = document.querySelectorAll('.filter-support');
  
  filterTabs.forEach(tab => {
    tab.addEventListener('click', function() {
      // Cập nhật tab active
      filterTabs.forEach(t => t.classList.remove('active'));
      this.classList.add('active');
      
      // Lọc theo trạng thái
      const status = this.getAttribute('data-status');
      filterSupportByStatus(status);
    });
  });
}

/**
 * Lọc yêu cầu hỗ trợ theo trạng thái
 */
function filterSupportByStatus(status) {
  const supportTableBody = document.getElementById('support-table-body');
  if (!supportTableBody) return;
  
  const rows = supportTableBody.querySelectorAll('tr[data-id]');
  
  rows.forEach(row => {
    if (status === 'all') {
      row.style.display = '';
      return;
    }
    
    const statusCell = row.querySelector('.support-status');
    if (!statusCell) return;
    
    const rowStatus = statusCell.classList.contains('new') ? 'new' :
                      statusCell.classList.contains('processing') ? 'processing' :
                      statusCell.classList.contains('resolved') ? 'resolved' : '';
    
    row.style.display = (rowStatus === status) ? '' : 'none';
  });
}

/**
 * Lọc yêu cầu hỗ trợ theo từ khóa
 */
function filterSupportRequests(keyword) {
  if (!keyword) {
    // Nếu không có từ khóa, hiển thị lại theo trạng thái đang chọn
    const activeTab = document.querySelector('.filter-support.active');
    if (activeTab) {
      const status = activeTab.getAttribute('data-status');
      filterSupportByStatus(status);
    }
    return;
  }
  
  const supportTableBody = document.getElementById('support-table-body');
  if (!supportTableBody) return;
  
  const rows = supportTableBody.querySelectorAll('tr[data-id]');
  
  rows.forEach(row => {
    const text = row.textContent.toLowerCase();
    row.style.display = text.includes(keyword) ? '' : 'none';
  });
}

/**
 * Cập nhật số lượng yêu cầu hỗ trợ theo trạng thái
 */
function updateSupportCounts(counts) {
  document.querySelectorAll('.support-count').forEach(element => {
    const status = element.getAttribute('data-status');
    if (counts[status] !== undefined) {
      element.textContent = counts[status];
    }
  });
}

/**
 * Xem chi tiết yêu cầu hỗ trợ
 */
function viewSupportRequest(id) {
  const request = supportRequests.find(r => r.id === id);
  
  if (!request) {
    showNotification('Không tìm thấy yêu cầu hỗ trợ', 'error');
    return;
  }
  
  // Hiển thị chi tiết
  alert(`
    Yêu cầu hỗ trợ #${request.id}
    
    Khách hàng: ${request.customerName || 'Khách hàng'}
    Email: ${request.email || 'N/A'}
    Điện thoại: ${request.phone || 'N/A'}
    Ngày gửi: ${formatDate(new Date(request.createdAt || new Date()))}
    
    Tiêu đề: ${request.subject || 'Không có tiêu đề'}
    
    Nội dung:
    ${request.message || 'Không có nội dung'}
    
    Trạng thái: ${
      !request.status || request.status === 'new' ? 'Mới' :
      request.status === 'processing' ? 'Đang xử lý' :
      request.status === 'resolved' ? 'Đã giải quyết' : ''
    }
  `);
  
  // Cập nhật trạng thái nếu là yêu cầu mới
  if (!request.status || request.status === 'new') {
    updateSupportStatus(id, 'processing');
  }
}

/**
 * Phản hồi yêu cầu hỗ trợ
 */
function replySupportRequest(id) {
  const request = supportRequests.find(r => r.id === id);
  
  if (!request) {
    showNotification('Không tìm thấy yêu cầu hỗ trợ', 'error');
    return;
  }
  
  const reply = prompt(`Phản hồi cho yêu cầu #${id}:`, '');
  
  if (reply === null) return; // Người dùng hủy
  
  if (reply.trim() === '') {
    alert('Vui lòng nhập nội dung phản hồi');
    return;
  }
  
  // Gửi phản hồi tới API
  fetch(`http://localhost:3000/support/${id}`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      status: 'processing',
      replies: [...(request.replies || []), {
        from: 'admin',
        message: reply,
        createdAt: new Date().toISOString()
      }]
    })
  })
    .then(response => {
      if (!response.ok) {
        throw new Error('Không thể cập nhật yêu cầu hỗ trợ');
      }
      return response.json();
    })
    .then(data => {
      showNotification('Đã gửi phản hồi thành công', 'success');
      loadSupportRequests();
    })
    .catch(error => {
      console.error('Lỗi khi gửi phản hồi:', error);
      
      // Cập nhật trong local
      const updatedRequest = {
        ...request,
        status: 'processing',
        replies: [...(request.replies || []), {
          from: 'admin',
          message: reply,
          createdAt: new Date().toISOString()
        }]
      };
      
      // Cập nhật trong danh sách
      const index = supportRequests.findIndex(r => r.id === id);
      if (index !== -1) {
        supportRequests[index] = updatedRequest;
      }
      
      // Cập nhật trong cache
      const cachedRequests = JSON.parse(localStorage.getItem('supportRequestsCache')) || [];
      const cachedIndex = cachedRequests.findIndex(r => r.id === id);
      if (cachedIndex !== -1) {
        cachedRequests[cachedIndex] = updatedRequest;
      } else {
        cachedRequests.push(updatedRequest);
      }
      localStorage.setItem('supportRequestsCache', JSON.stringify(cachedRequests));
      
      showNotification('Đã gửi phản hồi (chế độ ngoại tuyến)', 'warning');
      displaySupportRequests(supportRequests);
    });
}

/**
 * Đánh dấu yêu cầu hỗ trợ đã giải quyết
 */
function resolveSupportRequest(id) {
  if (!confirm('Bạn có chắc chắn muốn đánh dấu yêu cầu này là đã giải quyết?')) {
    return;
  }
  
  updateSupportStatus(id, 'resolved');
}

/**
 * Cập nhật trạng thái yêu cầu hỗ trợ
 */
function updateSupportStatus(id, status) {
  // Cập nhật trạng thái trên API
  fetch(`http://localhost:3000/support/${id}`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ status })
  })
    .then(response => {
      if (!response.ok) {
        throw new Error('Không thể cập nhật trạng thái yêu cầu hỗ trợ');
      }
      return response.json();
    })
    .then(data => {
      showNotification('Đã cập nhật trạng thái yêu cầu hỗ trợ', 'success');
      loadSupportRequests();
    })
    .catch(error => {
      console.error('Lỗi khi cập nhật trạng thái:', error);
      
      // Cập nhật trong local
      const request = supportRequests.find(r => r.id === id);
      if (request) {
        request.status = status;
      }
      
      // Cập nhật trong cache
      const cachedRequests = JSON.parse(localStorage.getItem('supportRequestsCache')) || [];
      const cachedRequest = cachedRequests.find(r => r.id === id);
      if (cachedRequest) {
        cachedRequest.status = status;
        localStorage.setItem('supportRequestsCache', JSON.stringify(cachedRequests));
      }
      
      showNotification('Đã cập nhật trạng thái (chế độ ngoại tuyến)', 'warning');
      displaySupportRequests(supportRequests);
    });
}

/**
 * Định dạng ngày tháng
 */
function formatDate(date) {
  return new Date(date).toLocaleDateString('vi-VN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}