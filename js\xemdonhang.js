/**
 * <PERSON><PERSON><PERSON> năng xem đơn hàng cho người dùng
 * File này tách chức năng xem đơn hàng từ taikhoan.js đ<PERSON> tránh trùng lặp
 */

// <PERSON><PERSON><PERSON> bảo lấy thông tin người dùng từ currentAccount toàn cục trong taikhoan.js
// Nếu không có biến toàn cục, lấy từ localStorage
let currentAccount = window.currentAccount || JSON.parse(localStorage.getItem('currentAccount')) || null;

function viewOrders() {
    if (!currentAccount) {
        openLoginModal();
        return;
    }
    
    // Tạo modal hiển thị danh sách đơn hàng
    let ordersModal = document.getElementById('orders-modal');
    if (!ordersModal) {
        ordersModal = createOrdersModal();
        document.body.appendChild(ordersModal);
    }
    
    // Hiển thị modal
    ordersModal.style.display = 'flex';
    
    // T<PERSON>i danh sách đơn hàng của người dùng
    loadUserOrders();
}

/**
 * Tạo modal hiển thị danh sách đơn hàng
 */
function createOrdersModal() {
    const modal = document.createElement('div');
    modal.id = 'orders-modal';
    modal.className = 'orders-modal';
    
    modal.innerHTML = `
        <div class="orders-content">
            <div class="orders-header">
                <h2>Đơn hàng của tôi</h2>
                <span class="close-btn">&times;</span>
            </div>
            <div class="orders-filters">
                <select id="order-status-filter">
                    <option value="all">Tất cả trạng thái</option>
                    <option value="processing">Đang xử lý</option>
                    <option value="confirmed">Đã xác nhận</option>
                    <option value="shipping">Đang giao hàng</option>
                    <option value="delivered">Đã giao hàng</option>
                    <option value="cancelled">Đã hủy</option>
                </select>
                <input type="text" id="order-search" placeholder="Tìm kiếm đơn hàng...">
            </div>
            <div class="orders-list">
                <!-- Danh sách đơn hàng sẽ được thêm vào đây -->
                <div id="orders-loading">Đang tải đơn hàng...</div>
                <div id="orders-empty" style="display: none;">Bạn chưa có đơn hàng nào</div>
                <div id="orders-container"></div>
            </div>
        </div>
    `;
    
    // Thêm sự kiện đóng modal
    modal.querySelector('.close-btn').addEventListener('click', function() {
        modal.style.display = 'none';
    });
    
    // Thêm sự kiện lọc đơn hàng
    modal.querySelector('#order-status-filter').addEventListener('change', function() {
        filterOrders();
    });
    
    // Thêm sự kiện tìm kiếm đơn hàng
    modal.querySelector('#order-search').addEventListener('input', function() {
        filterOrders();
    });
    
    // Đóng modal khi click bên ngoài
    window.addEventListener('click', function(event) {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
    
    return modal;
}

/**
 * Tải danh sách đơn hàng của người dùng
 */
function loadUserOrders() {
    // Cập nhật thông tin tài khoản từ localStorage
    currentAccount = JSON.parse(localStorage.getItem('currentAccount')) || currentAccount;
    
    if (!currentAccount) return;
    
    const ordersContainer = document.getElementById('orders-container');
    const loadingElement = document.getElementById('orders-loading');
    const emptyElement = document.getElementById('orders-empty');
    
    if (!ordersContainer || !loadingElement || !emptyElement) return;
    
    // Hiển thị loading
    loadingElement.style.display = 'block';
    emptyElement.style.display = 'none';
    ordersContainer.innerHTML = '';
    
    // Thử tải từ API trước
    fetch('http://localhost:3000/orders?userId=' + currentAccount.id)
        .then(response => {
            if (!response.ok) {
                throw new Error('Không thể kết nối đến API');
            }
            return response.json();
        })
        .then(orders => {
            renderOrders(orders);
        })
        .catch(error => {
            console.error('Lỗi khi tải đơn hàng từ API:', error);
            
            // Fallback: tải từ localStorage
            const allOrders = JSON.parse(localStorage.getItem('orders')) || [];
            const orderHistory = JSON.parse(localStorage.getItem('orderHistory')) || [];
            
            // Kết hợp cả hai nguồn và lọc theo userId
            const combinedOrders = [...allOrders, ...orderHistory]
                .filter(order => order.userId === currentAccount.id);
            
            renderOrders(combinedOrders);
        });
}

/**
 * Hiển thị danh sách đơn hàng
 */
function renderOrders(orders) {
    const ordersContainer = document.getElementById('orders-container');
    const loadingElement = document.getElementById('orders-loading');
    const emptyElement = document.getElementById('orders-empty');
    
    if (!ordersContainer || !loadingElement || !emptyElement) return;
    
    // Ẩn loading
    loadingElement.style.display = 'none';
    
    // Kiểm tra nếu không có đơn hàng nào
    if (!orders || orders.length === 0) {
        emptyElement.style.display = 'block';
        return;
    }
    
    // Sắp xếp đơn hàng theo thời gian giảm dần (mới nhất lên đầu)
    orders.sort((a, b) => new Date(b.orderDate) - new Date(a.orderDate));
    
    // Hiển thị danh sách đơn hàng
    orders.forEach(order => {
        const orderCard = document.createElement('div');
        orderCard.className = 'order-card';
        orderCard.setAttribute('data-order-id', order.id);
        orderCard.setAttribute('data-order-status', order.orderStatus || 'processing');
        
        // Format ngày tháng
        const orderDate = new Date(order.orderDate);
        const formattedDate = orderDate.toLocaleDateString('vi-VN', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
        
        // Xác định trạng thái đơn hàng
        let statusText = 'Đang xử lý';
        let statusClass = 'status-processing';
        
        switch (order.orderStatus) {
            case 'confirmed':
                statusText = 'Đã xác nhận';
                statusClass = 'status-confirmed';
                break;
            case 'shipping':
                statusText = 'Đang giao hàng';
                statusClass = 'status-shipping';
                break;
            case 'delivered':
                statusText = 'Đã giao hàng';
                statusClass = 'status-delivered';
                break;
            case 'cancelled':
                statusText = 'Đã hủy';
                statusClass = 'status-cancelled';
                break;
        }
        
        // Tính tổng số lượng sản phẩm
        const totalItems = order.items.reduce((total, item) => total + item.quantity, 0);
        
        orderCard.innerHTML = `
            <div class="order-header">
                <div class="order-id">
                    <span class="label">Mã đơn hàng:</span>
                    <span class="value">${order.id}</span>
                </div>
                <div class="order-date">
                    <span class="label">Ngày đặt:</span>
                    <span class="value">${formattedDate}</span>
                </div>
                <div class="order-status ${statusClass}">
                    <span class="status-text">${statusText}</span>
                </div>
            </div>
            <div class="order-summary">
                <div class="order-products">
                    <span class="label">Số sản phẩm:</span>
                    <span class="value">${totalItems}</span>
                </div>
                <div class="order-total">
                    <span class="label">Tổng tiền:</span>
                    <span class="value">${formatPrice(order.total)}</span>
                </div>
            </div>
            <div class="order-actions">
                <button class="btn-view-detail" data-order-id="${order.id}">Xem chi tiết</button>
                ${order.orderStatus === 'processing' ? `<button class="btn-cancel-order" data-order-id="${order.id}">Hủy đơn</button>` : ''}
            </div>
        `;
        
        ordersContainer.appendChild(orderCard);
        
        // Thêm sự kiện xem chi tiết đơn hàng
        orderCard.querySelector('.btn-view-detail').addEventListener('click', function() {
            const orderId = this.getAttribute('data-order-id');
            viewOrderDetail(orderId);
        });
        
        // Thêm sự kiện hủy đơn hàng nếu có
        const cancelBtn = orderCard.querySelector('.btn-cancel-order');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', function() {
                const orderId = this.getAttribute('data-order-id');
                confirmCancelOrder(orderId);
            });
        }
    });
    
    // Áp dụng bộ lọc hiện tại
    filterOrders();
}

/**
 * Lọc danh sách đơn hàng theo trạng thái và từ khóa tìm kiếm
 */
function filterOrders() {
    const statusFilter = document.getElementById('order-status-filter').value;
    const searchFilter = document.getElementById('order-search').value.toLowerCase();
    const orderCards = document.querySelectorAll('.order-card');
    
    orderCards.forEach(card => {
        const orderId = card.getAttribute('data-order-id').toLowerCase();
        const orderStatus = card.getAttribute('data-order-status');
        const orderText = card.textContent.toLowerCase();
        
        // Áp dụng bộ lọc trạng thái
        const statusMatch = statusFilter === 'all' || orderStatus === statusFilter;
        
        // Áp dụng bộ lọc tìm kiếm
        const searchMatch = !searchFilter || 
            orderId.includes(searchFilter) || 
            orderText.includes(searchFilter);
        
        // Hiển thị hoặc ẩn card tùy theo kết quả lọc
        card.style.display = statusMatch && searchMatch ? 'block' : 'none';
    });
}

/**
 * Hiển thị chi tiết đơn hàng
 */
function viewOrderDetail(orderId) {
    // Tìm đơn hàng từ localStorage
    const allOrders = JSON.parse(localStorage.getItem('orders')) || [];
    const orderHistory = JSON.parse(localStorage.getItem('orderHistory')) || [];
    
    // Kết hợp các nguồn dữ liệu
    const combinedOrders = [...allOrders, ...orderHistory];
    const order = combinedOrders.find(o => o.id === orderId);
    
    if (!order) {
        console.error('Không tìm thấy đơn hàng:', orderId);
        alert('Không tìm thấy thông tin đơn hàng');
        return;
    }
    
    showOrderDetailModal(order);
}

/**
 * Hiển thị modal chi tiết đơn hàng
 */
function showOrderDetailModal(order) {
    let detailModal = document.getElementById('order-detail-modal');
    
    if (!detailModal) {
        detailModal = document.createElement('div');
        detailModal.id = 'order-detail-modal';
        detailModal.className = 'order-detail-modal';
        document.body.appendChild(detailModal);
    }
    
    // Format ngày tháng
    const orderDate = new Date(order.orderDate);
    const formattedDate = orderDate.toLocaleDateString('vi-VN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
    
    // Xác định trạng thái đơn hàng
    let statusText = 'Đang xử lý';
    let statusClass = 'status-processing';
    
    switch (order.orderStatus) {
        case 'confirmed':
            statusText = 'Đã xác nhận';
            statusClass = 'status-confirmed';
            break;
        case 'shipping':
            statusText = 'Đang giao hàng';
            statusClass = 'status-shipping';
            break;
        case 'delivered':
            statusText = 'Đã giao hàng';
            statusClass = 'status-delivered';
            break;
        case 'cancelled':
            statusText = 'Đã hủy';
            statusClass = 'status-cancelled';
            break;
    }
    
    // Tạo danh sách sản phẩm
    let productsHTML = '';
    let subtotal = 0;
    
    order.items.forEach(item => {
        const itemTotal = item.price * item.quantity;
        subtotal += itemTotal;
        
        productsHTML += `
            <div class="order-product-item">
                <div class="product-image">
                    <img src="${item.image || 'images/default-product.jpg'}" alt="${item.productName}">
                </div>
                <div class="product-details">
                    <div class="product-name">${item.productName}</div>
                    <div class="product-quantity">Số lượng: ${item.quantity}</div>
                    <div class="product-price">${formatPrice(item.price)}</div>
                </div>
                <div class="product-total">
                    ${formatPrice(itemTotal)}
                </div>
            </div>
        `;
    });
    
    // Tính các giá trị khác
    const shipping = order.shipping || 0;
    const discount = order.discount || 0;
    const total = order.total || (subtotal + shipping - discount);
    
    // Phương thức thanh toán
    let paymentMethod = 'Thanh toán khi nhận hàng (COD)';
    
    if (order.paymentMethod === 'bank') {
        paymentMethod = 'Chuyển khoản ngân hàng';
    } else if (order.paymentMethod === 'momo') {
        paymentMethod = 'Ví MoMo';
    } else if (order.paymentMethod === 'credit') {
        paymentMethod = 'Thẻ tín dụng/ghi nợ';
    }
    
    detailModal.innerHTML = `
        <div class="detail-content">
            <div class="detail-header">
                <h2>Chi tiết đơn hàng #${order.id}</h2>
                <span class="close-btn">&times;</span>
            </div>
            
            <div class="detail-section order-info">
                <div class="order-status-large ${statusClass}">
                    <span class="status-text">${statusText}</span>
                </div>
                
                <div class="detail-row">
                    <div class="detail-column">
                        <h3>Thông tin đơn hàng</h3>
                        <p><strong>Mã đơn hàng:</strong> ${order.id}</p>
                        <p><strong>Ngày đặt:</strong> ${formattedDate}</p>
                        <p><strong>Phương thức thanh toán:</strong> ${paymentMethod}</p>
                    </div>
                    
                    <div class="detail-column">
                        <h3>Thông tin nhận hàng</h3>
                        <p><strong>Người nhận:</strong> ${order.shippingInfo?.fullName || 'N/A'}</p>
                        <p><strong>Số điện thoại:</strong> ${order.shippingInfo?.phone || 'N/A'}</p>
                        <p><strong>Địa chỉ:</strong> ${order.shippingInfo?.address || 'N/A'}</p>
                    </div>
                </div>
            </div>
            
            <div class="detail-section order-products">
                <h3>Danh sách sản phẩm</h3>
                <div class="product-list">
                    ${productsHTML}
                </div>
            </div>
            
            <div class="detail-section order-summary">
                <h3>Tổng cộng</h3>
                <div class="summary-table">
                    <div class="summary-row">
                        <div class="summary-label">Tạm tính:</div>
                        <div class="summary-value">${formatPrice(subtotal)}</div>
                    </div>
                    <div class="summary-row">
                        <div class="summary-label">Phí vận chuyển:</div>
                        <div class="summary-value">${formatPrice(shipping)}</div>
                    </div>
                    <div class="summary-row">
                        <div class="summary-label">Giảm giá:</div>
                        <div class="summary-value">-${formatPrice(discount)}</div>
                    </div>
                    <div class="summary-row total">
                        <div class="summary-label">Tổng tiền:</div>
                        <div class="summary-value">${formatPrice(total)}</div>
                    </div>
                </div>
            </div>
            
            <div class="detail-actions">
                <button class="btn-close-detail">Đóng</button>
                ${order.orderStatus === 'processing' ? 
                    `<button class="btn-cancel-order-detail" data-order-id="${order.id}">Hủy đơn hàng</button>` : ''}
            </div>
        </div>
    `;
    
    // Thêm sự kiện đóng modal
    detailModal.querySelector('.close-btn').addEventListener('click', function() {
        detailModal.style.display = 'none';
    });
    
    detailModal.querySelector('.btn-close-detail').addEventListener('click', function() {
        detailModal.style.display = 'none';
    });
    
    // Thêm sự kiện hủy đơn hàng nếu có
    const cancelDetailBtn = detailModal.querySelector('.btn-cancel-order-detail');
    if (cancelDetailBtn) {
        cancelDetailBtn.addEventListener('click', function() {
            const orderId = this.getAttribute('data-order-id');
            confirmCancelOrder(orderId);
            detailModal.style.display = 'none';
        });
    }
    
    // Hiển thị modal
    detailModal.style.display = 'flex';
    
    // Đóng modal khi click bên ngoài
    window.addEventListener('click', function(event) {
        if (event.target === detailModal) {
            detailModal.style.display = 'none';
        }
    });
}

/**
 * Xác nhận hủy đơn hàng
 */
function confirmCancelOrder(orderId) {
    if (confirm('Bạn có chắc chắn muốn hủy đơn hàng này không?')) {
        cancelOrder(orderId);
    }
}

/**
 * Hủy đơn hàng
 */
function cancelOrder(orderId) {
    // Thử hủy đơn hàng qua API trước
    fetch(`http://localhost:3000/orders/${orderId}`, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            orderStatus: 'cancelled',
            updatedAt: new Date().toISOString()
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Không thể hủy đơn hàng qua API');
        }
        return response.json();
    })
    .then(data => {
        alert('Đơn hàng đã được hủy thành công!');
        loadUserOrders();
    })
    .catch(error => {
        console.error('Lỗi khi hủy đơn hàng qua API:', error);
        
        // Fallback: cập nhật trong localStorage
        if (updateLocalOrderStatus(orderId, 'cancelled')) {
            alert('Đơn hàng đã được hủy thành công!');
            loadUserOrders();
        } else {
            alert('Không thể hủy đơn hàng. Vui lòng thử lại sau!');
        }
    });
}

/**
 * Cập nhật trạng thái đơn hàng trong localStorage
 */
function updateLocalOrderStatus(orderId, newStatus) {
    let updated = false;
    
    // Cập nhật trong orders
    const allOrders = JSON.parse(localStorage.getItem('orders')) || [];
    const orderIndex = allOrders.findIndex(o => o.id === orderId);
    
    if (orderIndex !== -1) {
        allOrders[orderIndex].orderStatus = newStatus;
        allOrders[orderIndex].updatedAt = new Date().toISOString();
        localStorage.setItem('orders', JSON.stringify(allOrders));
        updated = true;
    }
    
    // Cập nhật trong orderHistory
    const orderHistory = JSON.parse(localStorage.getItem('orderHistory')) || [];
    const historyIndex = orderHistory.findIndex(o => o.id === orderId);
    
    if (historyIndex !== -1) {
        orderHistory[historyIndex].orderStatus = newStatus;
        orderHistory[historyIndex].updatedAt = new Date().toISOString();
        localStorage.setItem('orderHistory', JSON.stringify(orderHistory));
        updated = true;
    }
    
    return updated;
}

/**
 * Format số tiền thành định dạng tiền tệ
 */
function formatPrice(price) {
    return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
    }).format(price).replace('₫', 'đ');
}