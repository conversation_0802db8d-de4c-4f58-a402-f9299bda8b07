/* ======================== Dropdown Styles ======================== */

.header-middle-right-item {
    position: relative; /* For positioning dropdown */
}

.account-dropdown-wrapper {
    display: none;
    position: absolute;
    top: calc(100% - 6px);
    left: 0;
    width: 140px;
    margin-top: 5px;
    background: white;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
    border-radius: 5px;
    opacity: 0;
    pointer-events: none;
    transform: translateY(10px);
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-out, box-shadow 0.3s ease-in-out;
    z-index: 99999;
}

.header-middle-right-item.dropdown.open:hover .account-dropdown-wrapper,
.header-middle-right-item.dropdown.open .account-dropdown-wrapper:hover {
    display: block;
    opacity: 1;
    pointer-events: auto;
    transform: translateY(0);
}

.account-dropdown-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.account-dropdown-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s ease-in-out, transform 0.3s ease-in-out;
}

.account-dropdown-item i {
    margin-right: 10px;
}

.account-dropdown-item:hover {
    background-color: #f0f0f0;
    color: #007bff;
    transform: scale(1.05);
}

/* ======================== Modal Styles ======================== */

.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    position: relative;
    width: 90%;
    max-width: 400px;
    padding: 30px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    animation: fadeIn 0.3s ease;
}

.modal-title {
    font-size: 20px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 10px;
}

.modal-description {
    font-size: 14px;
    text-align: center;
    color: #666;
    margin-bottom: 20px;
}

.modal-close {
    position: absolute;
    top: 12px;
    right: 15px;
    font-size: 24px;
    font-weight: bold;
    color: #888;
    cursor: pointer;
}

.modal-input {
    width: 100%;
    padding: 12px;
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
}

.modal-checkbox-wrapper {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: #333;
    margin-bottom: 15px;
}

.modal-checkbox-wrapper input[type="checkbox"] {
    margin-right: 8px;
}

.modal-policy-link {
    color: #d0312d;
    text-decoration: none;
}

.modal-button {
    width: 100%;
    padding: 12px;
    background-color: #b92025;
    color: #fff;
    border: none;
    border-radius: 8px;
    font-weight: bold;
    cursor: pointer;
    font-size: 15px;
}

.modal-switch-text {
    text-align: center;
    margin-top: 15px;
    font-size: 14px;
}

.modal-switch-link {
    color: #d0312d;
    text-decoration: none;
    font-weight: bold;
    margin-left: 5px;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}
