/**
 * <PERSON><PERSON><PERSON> quản lý đơn hàng cho Admin
 * - <PERSON><PERSON><PERSON> thị danh sách đơn hàng
 * - Cập nhật trạng thái đơn hàng
 * - Xem chi tiết đơn hàng
 */

// API Endpoints
const API_BASE_URL = "https://json-api-service.onrender.com";
const ORDERS_API = API_BASE_URL + "/orders";

document.addEventListener('DOMContentLoaded', function() {
  // Khởi tạo giao diện quản lý đơn hàng
  initOrderManagement();
  
  // Tải danh sách đơn hàng
  loadOrders();
  
  // Thiết lập các bộ lọc
  setupFilters();
  
  // Thiết lập các nút hành động
  setupActionButtons();
  
  // Lắng nghe sự kiện đơn hàng mới được tạo
  document.addEventListener('orderCreated', function(event) {
    console.log('<PERSON>min đã nhận được sự kiện đơn hàng mới:', event.detail.order);
    // Tải lại danh sách đơn hàng
    loadOrders();
  });
});

/**
 * Khởi tạo giao diện quản lý đơn hàng
 */
function initOrderManagement() {
  // Xử lý khi nhấp vào menu đơn hàng
  const orderMenu = document.getElementById('order-management-menu');
  if (orderMenu) {
    orderMenu.addEventListener('click', function(e) {
      e.preventDefault();
      showOrderManagement();
    });
  }
  
  // Xử lý các sự kiện cho bảng đơn hàng
  document.addEventListener('click', function(e) {
    // Xem chi tiết đơn hàng
    if (e.target.matches('.btn-view-order') || e.target.closest('.btn-view-order')) {
      const orderId = e.target.closest('.btn-view-order').getAttribute('data-order-id');
      viewOrderDetails(orderId);
    }
    
    // Cập nhật trạng thái đơn hàng
    if (e.target.matches('.order-status-select')) {
      const orderId = e.target.getAttribute('data-order-id');
      const newStatus = e.target.value;
      updateOrderStatus(orderId, newStatus);
    }
    
    // Xử lý các bộ lọc
    if (e.target.matches('.filter-status')) {
      const status = e.target.getAttribute('data-status');
      filterOrdersByStatus(status);
    }
    
    // Xóa đơn hàng
    if (e.target.matches('.btn-delete-order') || e.target.closest('.btn-delete-order')) {
      const orderId = e.target.closest('.btn-delete-order').getAttribute('data-order-id');
      deleteOrder(orderId);
    }
  });
}

/**
 * Tải danh sách đơn hàng từ API và localStorage
 */
function loadOrders() {
  // Hiển thị trạng thái loading
  const orderTable = document.getElementById('order-table-body');
  if (orderTable) {
    orderTable.innerHTML = `
      <tr>
        <td colspan="7" class="text-center">
          <div class="loading-spinner"></div>
          <p>Đang tải danh sách đơn hàng...</p>
        </td>
      </tr>
    `;
  }
  
  // Tải từ API nếu có kết nối
  fetchOrdersFromApi()
    .then(apiOrders => {
      // Lấy dữ liệu từ localStorage
      const localOrders = JSON.parse(localStorage.getItem('orders')) || [];
      const orderHistory = JSON.parse(localStorage.getItem('orderHistory')) || [];
      
      // Kết hợp tất cả các nguồn dữ liệu
      const allOrders = [...apiOrders];
      
      // Thêm các đơn hàng từ localStorage nếu chưa có trong API
      [...localOrders, ...orderHistory].forEach(localOrder => {
        if (!allOrders.some(apiOrder => apiOrder.id === localOrder.id)) {
          allOrders.push(localOrder);
        }
      });
      
      // Sắp xếp đơn hàng theo thời gian giảm dần (mới nhất lên đầu)
      allOrders.sort((a, b) => new Date(b.orderDate || b.createdAt || 0) - new Date(a.orderDate || a.createdAt || 0));
      
      // Lưu danh sách đơn hàng đã gộp để đồng bộ
      localStorage.setItem('allOrdersCache', JSON.stringify(allOrders));
      
      // Hiển thị danh sách đơn hàng
      displayOrders(allOrders);
      
      // Cập nhật số lượng
      updateOrderCounts();
      
      return allOrders;
    })
    .catch(error => {
      console.error('Lỗi khi tải đơn hàng từ API:', error);
      
      // Tải từ localStorage nếu không có kết nối API
      const localOrders = JSON.parse(localStorage.getItem('orders')) || [];
      const orderHistory = JSON.parse(localStorage.getItem('orderHistory')) || [];
      const cachedOrders = JSON.parse(localStorage.getItem('allOrdersCache')) || [];
      
      // Kết hợp tất cả các nguồn dữ liệu
      const allOrders = [...cachedOrders];
      
      // Thêm các đơn hàng từ localStorage nếu chưa có trong cache
      [...localOrders, ...orderHistory].forEach(localOrder => {
        if (!allOrders.some(cachedOrder => cachedOrder.id === localOrder.id)) {
          allOrders.push(localOrder);
        }
      });
      
      // Sắp xếp đơn hàng theo thời gian giảm dần (mới nhất lên đầu)
      allOrders.sort((a, b) => new Date(b.orderDate || b.createdAt || 0) - new Date(a.orderDate || a.createdAt || 0));
      
      // Hiển thị danh sách đơn hàng
      displayOrders(allOrders);
      
      // Cập nhật số lượng
      updateOrderCounts();
      
      // Hiển thị thông báo
      showNotification('Đang hiển thị dữ liệu ngoại tuyến', 'warning');
      
      return allOrders;
    });
}

/**
 * Tải đơn hàng từ API
 */
function fetchOrdersFromApi() {
  return fetch(ORDERS_API)
    .then(response => {
      if (!response.ok) {
        throw new Error('Không thể kết nối đến API');
      }
      return response.json();
    });
}

/**
 * Đồng bộ hóa đơn hàng giữa API và localStorage
 */
function syncOrders() {
  // Hiển thị thông báo đang đồng bộ
  showNotification('Đang đồng bộ dữ liệu đơn hàng...', 'info');
  
  // Lấy dữ liệu từ localStorage
  const localOrders = JSON.parse(localStorage.getItem('orders')) || [];
  const orderHistory = JSON.parse(localStorage.getItem('orderHistory')) || [];
  
  // Kết hợp tất cả đơn hàng từ localStorage
  const allLocalOrders = [...localOrders, ...orderHistory];
  
  // Lọc ra các đơn hàng chưa được đồng bộ
  const unsyncedOrders = allLocalOrders.filter(order => !order.synced);
  
  if (unsyncedOrders.length === 0) {
    showNotification('Không có đơn hàng mới cần đồng bộ', 'info');
    // Vẫn tải lại để cập nhật từ server
    loadOrders();
    return Promise.resolve([]);
  }
  
  // Đồng bộ lên API
  const syncPromises = unsyncedOrders.map(order => {
    return fetch(ORDERS_API, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(order)
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`Không thể đồng bộ đơn hàng ${order.id}`);
      }
      return response.json();
    })
    .then(data => {
      console.log(`Đơn hàng ${order.id} đã được đồng bộ lên API`);
      return data;
    })
    .catch(error => {
      console.error(`Lỗi khi đồng bộ đơn hàng ${order.id}:`, error);
      return null;
    });
  });
  
  return Promise.all(syncPromises)
    .then(results => {
      // Đánh dấu đơn hàng đã được đồng bộ
      const successCount = results.filter(result => result !== null).length;
      
      // Cập nhật trạng thái đồng bộ
      if (successCount > 0) {
        const updatedLocalOrders = localOrders.map(order => {
          if (unsyncedOrders.some(unsynced => unsynced.id === order.id)) {
            return { ...order, synced: true };
          }
          return order;
        });
        
        const updatedOrderHistory = orderHistory.map(order => {
          if (unsyncedOrders.some(unsynced => unsynced.id === order.id)) {
            return { ...order, synced: true };
          }
          return order;
        });
        
        // Lưu lại vào localStorage
        localStorage.setItem('orders', JSON.stringify(updatedLocalOrders));
        localStorage.setItem('orderHistory', JSON.stringify(updatedOrderHistory));
        
        showNotification(`Đã đồng bộ ${successCount}/${unsyncedOrders.length} đơn hàng lên hệ thống`, 'success');
      }
      
      // Tải lại danh sách đơn hàng
      loadOrders();
      
      return results;
    });
}

/**
 * Hiển thị danh sách đơn hàng
 */
function displayOrders(orders) {
  const orderTable = document.getElementById('order-table-body');
  if (!orderTable) return;
  
  if (orders.length === 0) {
    orderTable.innerHTML = `
      <tr>
        <td colspan="7" class="text-center">
          <p>Không có đơn hàng nào</p>
        </td>
      </tr>
    `;
    return;
  }
  
  // Sắp xếp đơn hàng theo thời gian giảm dần (mới nhất lên đầu)
  orders.sort((a, b) => new Date(b.orderDate) - new Date(a.orderDate));
  
  orderTable.innerHTML = '';
  
  orders.forEach(order => {
    const tr = document.createElement('tr');
    tr.setAttribute('data-order-id', order.id);
    
    // Xác định class dựa trên trạng thái đơn hàng
    if (order.orderStatus === 'processing') {
      tr.classList.add('order-processing');
    } else if (order.orderStatus === 'completed') {
      tr.classList.add('order-completed');
    } else if (order.orderStatus === 'cancelled') {
      tr.classList.add('order-cancelled');
    }
    
    tr.innerHTML = `
      <td>${order.id}</td>
      <td>${order.customerName}</td>
      <td>${formatDate(order.orderDate)}</td>
      <td>${formatPrice(order.total)}</td>
      <td>
        <span class="order-status status-${order.orderStatus}">${getOrderStatusText(order.orderStatus)}</span>
      </td>
      <td>
        <select class="order-status-select" data-order-id="${order.id}">
          <option value="processing" ${order.orderStatus === 'processing' ? 'selected' : ''}>Đang xử lý</option>
          <option value="confirmed" ${order.orderStatus === 'confirmed' ? 'selected' : ''}>Đã xác nhận</option>
          <option value="shipping" ${order.orderStatus === 'shipping' ? 'selected' : ''}>Đang giao hàng</option>
          <option value="delivered" ${order.orderStatus === 'delivered' ? 'selected' : ''}>Đã giao hàng</option>
          <option value="completed" ${order.orderStatus === 'completed' ? 'selected' : ''}>Hoàn thành</option>
          <option value="cancelled" ${order.orderStatus === 'cancelled' ? 'selected' : ''}>Đã hủy</option>
        </select>
      </td>
      <td>
        <div class="order-actions">
          <button class="btn-view-order" data-order-id="${order.id}" title="Xem chi tiết">
            <i class="fas fa-eye"></i>
          </button>
          <button class="btn-delete-order" data-order-id="${order.id}" title="Xóa đơn hàng">
            <i class="fas fa-trash"></i>
          </button>
        </div>
      </td>
    `;
    
    orderTable.appendChild(tr);
  });
  
  // Cập nhật số lượng đơn hàng
  const orderCount = document.getElementById('order-count');
  if (orderCount) {
    orderCount.textContent = orders.length;
  }
}

/**
 * Cập nhật trạng thái đơn hàng
 */
function updateOrderStatus(orderId, newStatus) {
  // Tìm đơn hàng trong localStorage
  const orders = JSON.parse(localStorage.getItem('orders')) || [];
  const orderIndex = orders.findIndex(order => order.id === orderId);
  
  if (orderIndex === -1) {
    console.error('Không tìm thấy đơn hàng');
    return;
  }
  
  // Cập nhật trạng thái
  const oldStatus = orders[orderIndex].orderStatus;
  orders[orderIndex].orderStatus = newStatus;
  
  // Thêm timestamp cho trạng thái mới
  const now = new Date().toISOString();
  
  switch (newStatus) {
    case 'confirmed':
      orders[orderIndex].confirmedAt = now;
      break;
    case 'shipping':
      orders[orderIndex].shippingAt = now;
      break;
    case 'delivered':
      orders[orderIndex].deliveredAt = now;
      break;
    case 'completed':
      orders[orderIndex].completedAt = now;
      break;
    case 'cancelled':
      orders[orderIndex].cancelledAt = now;
      break;
  }
  
  // Cập nhật thời gian cập nhật
  orders[orderIndex].updatedAt = now;
  
  // Lưu vào localStorage
  localStorage.setItem('orders', JSON.stringify(orders));
  
  // Cập nhật giao diện
  const orderRow = document.querySelector(`tr[data-order-id="${orderId}"]`);
  if (orderRow) {
    // Xóa các classes cũ
    orderRow.classList.remove('order-processing', 'order-completed', 'order-cancelled');
    
    // Thêm class mới
    if (newStatus === 'processing') {
      orderRow.classList.add('order-processing');
    } else if (newStatus === 'completed') {
      orderRow.classList.add('order-completed');
    } else if (newStatus === 'cancelled') {
      orderRow.classList.add('order-cancelled');
    }
    
    // Cập nhật text hiển thị trạng thái
    const statusCell = orderRow.querySelector('.order-status');
    if (statusCell) {
      statusCell.className = `order-status status-${newStatus}`;
      statusCell.textContent = getOrderStatusText(newStatus);
    }
  }
  
  // Hiển thị thông báo
  showNotification(`Đã cập nhật trạng thái đơn hàng từ ${getOrderStatusText(oldStatus)} thành ${getOrderStatusText(newStatus)}`);
  
  // Cập nhật lên API
  updateOrderToApi(orderId, {
    orderStatus: newStatus,
    updatedAt: now
  });
}

/**
 * Cập nhật đơn hàng lên API
 */
function updateOrderToApi(orderId, updateData) {
  fetch(`${ORDERS_API}/${orderId}`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(updateData)
  })
  .then(response => {
    if (!response.ok) {
      throw new Error('Không thể cập nhật đơn hàng lên API');
    }
    return response.json();
  })
  .then(data => {
    console.log('Cập nhật đơn hàng thành công:', data);
  })
  .catch(error => {
    console.error('Lỗi khi cập nhật đơn hàng:', error);
    // Đã lưu vào localStorage nên không cần xử lý thêm
  });
}

/**
 * Xem chi tiết đơn hàng
 */
function viewOrderDetails(orderId) {
  // Tìm đơn hàng từ localStorage
  const orders = JSON.parse(localStorage.getItem('orders')) || [];
  const order = orders.find(order => order.id === orderId);
  
  if (!order) {
    // Nếu không tìm thấy trong localStorage, thử lấy từ API
    fetchOrderFromApi(orderId);
    return;
  }
  
  displayOrderDetails(order);
}

/**
 * Tải thông tin đơn hàng từ API
 */
function fetchOrderFromApi(orderId) {
  fetch(`${ORDERS_API}/${orderId}`)
    .then(response => {
      if (!response.ok) {
        throw new Error('Không tìm thấy đơn hàng');
      }
      return response.json();
    })
    .then(order => {
      displayOrderDetails(order);
    })
    .catch(error => {
      console.error('Lỗi khi tải thông tin đơn hàng:', error);
      showNotification('Không thể tải thông tin đơn hàng', 'error');
    });
}

/**
 * Hiển thị chi tiết đơn hàng
 */
function displayOrderDetails(order) {
  let orderDetailModal = document.getElementById('admin-order-detail');
  if (!orderDetailModal) {
    orderDetailModal = document.createElement('div');
    orderDetailModal.id = 'admin-order-detail';
    orderDetailModal.className = 'admin-modal';
    
    orderDetailModal.innerHTML = `
      <div class="admin-modal-content">
        <span class="close-btn">&times;</span>
        <div class="modal-header">
          <h2>Chi tiết đơn hàng</h2>
          <div class="order-status">
            <span class="status-label">Trạng thái:</span>
            <span class="status-value"></span>
          </div>
        </div>
        
        <div class="modal-body">
          <div class="order-detail-info">
            <div class="info-section">
              <h3>Thông tin đơn hàng</h3>
              <table class="info-table">
                <tr>
                  <td>Mã đơn hàng:</td>
                  <td id="detail-order-id"></td>
                </tr>
                <tr>
                  <td>Ngày đặt hàng:</td>
                  <td id="detail-order-date"></td>
                </tr>
                <tr>
                  <td>Cập nhật lần cuối:</td>
                  <td id="detail-updated-at"></td>
                </tr>
              </table>
            </div>
            
            <div class="info-section">
              <h3>Thông tin khách hàng</h3>
              <table class="info-table">
                <tr>
                  <td>Khách hàng:</td>
                  <td id="detail-customer-name"></td>
                </tr>
                <tr>
                  <td>Tài khoản:</td>
                  <td id="detail-username"></td>
                </tr>
                <tr>
                  <td>Số điện thoại:</td>
                  <td id="detail-phone"></td>
                </tr>
                <tr>
                  <td>Địa chỉ:</td>
                  <td id="detail-address"></td>
                </tr>
                <tr>
                  <td>Ghi chú:</td>
                  <td id="detail-note"></td>
                </tr>
              </table>
            </div>
            
            <div class="info-section">
              <h3>Thông tin thanh toán</h3>
              <table class="info-table">
                <tr>
                  <td>Phương thức:</td>
                  <td id="detail-payment-method"></td>
                </tr>
                <tr>
                  <td>Trạng thái:</td>
                  <td id="detail-payment-status"></td>
                </tr>
              </table>
            </div>
          </div>
          
          <div class="order-items-section">
            <h3>Sản phẩm</h3>
            <table class="items-table">
              <thead>
                <tr>
                  <th>Sản phẩm</th>
                  <th>Đơn giá</th>
                  <th>Số lượng</th>
                  <th>Thành tiền</th>
                </tr>
              </thead>
              <tbody id="detail-items">
                <!-- Chi tiết sản phẩm sẽ được thêm vào đây -->
              </tbody>
              <tfoot>
                <tr>
                  <td colspan="3" class="text-right">Tạm tính:</td>
                  <td id="detail-subtotal"></td>
                </tr>
                <tr>
                  <td colspan="3" class="text-right">Phí vận chuyển:</td>
                  <td id="detail-shipping"></td>
                </tr>
                <tr class="order-total">
                  <td colspan="3" class="text-right">Tổng cộng:</td>
                  <td id="detail-total"></td>
                </tr>
              </tfoot>
            </table>
          </div>
          
          <div class="order-timeline">
            <h3>Lịch sử đơn hàng</h3>
            <div class="timeline" id="order-timeline">
              <!-- Timeline sẽ được thêm vào đây -->
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <div class="status-update">
            <select id="admin-status-select">
              <option value="processing">Đang xử lý</option>
              <option value="confirmed">Đã xác nhận</option>
              <option value="shipping">Đang giao hàng</option>
              <option value="delivered">Đã giao hàng</option>
              <option value="completed">Hoàn thành</option>
              <option value="cancelled">Đã hủy</option>
            </select>
            <button id="btn-update-status">Cập nhật trạng thái</button>
          </div>
          <button class="btn-close">Đóng</button>
        </div>
      </div>
    `;
    
    document.body.appendChild(orderDetailModal);
    
    // Xử lý sự kiện đóng modal
    orderDetailModal.querySelector('.close-btn').addEventListener('click', function() {
      orderDetailModal.style.display = 'none';
    });
    
    orderDetailModal.querySelector('.btn-close').addEventListener('click', function() {
      orderDetailModal.style.display = 'none';
    });
    
    // Xử lý sự kiện cập nhật trạng thái
    orderDetailModal.querySelector('#btn-update-status').addEventListener('click', function() {
      const orderId = orderDetailModal.getAttribute('data-order-id');
      const newStatus = orderDetailModal.querySelector('#admin-status-select').value;
      
      updateOrderStatus(orderId, newStatus);
      // Cập nhật lại chi tiết đơn hàng
      viewOrderDetails(orderId);
    });
  }
  
  // Lưu orderId vào modal
  orderDetailModal.setAttribute('data-order-id', order.id);
  
  // Cập nhật thông tin đơn hàng
  orderDetailModal.querySelector('#detail-order-id').textContent = order.id;
  orderDetailModal.querySelector('#detail-order-date').textContent = formatDate(order.orderDate);
  orderDetailModal.querySelector('#detail-updated-at').textContent = order.updatedAt ? formatDate(order.updatedAt) : 'N/A';
  
  orderDetailModal.querySelector('#detail-customer-name').textContent = order.customerName;
  orderDetailModal.querySelector('#detail-username').textContent = order.username || 'N/A';
  orderDetailModal.querySelector('#detail-phone').textContent = order.phone;
  orderDetailModal.querySelector('#detail-address').textContent = order.address;
  orderDetailModal.querySelector('#detail-note').textContent = order.note || 'Không có';
  
  orderDetailModal.querySelector('#detail-payment-method').textContent = getPaymentMethodText(order.paymentMethod);
  
  let paymentStatusText = 'Chưa thanh toán';
  if (order.paymentStatus === 'paid') {
    paymentStatusText = 'Đã thanh toán';
  } else if (order.paymentStatus === 'waiting') {
    paymentStatusText = 'Chờ thanh toán';
  }
  orderDetailModal.querySelector('#detail-payment-status').textContent = paymentStatusText;
  
  // Cập nhật trạng thái đơn hàng
  const statusValue = orderDetailModal.querySelector('.status-value');
  statusValue.textContent = getOrderStatusText(order.orderStatus);
  statusValue.className = 'status-value status-' + order.orderStatus;
  
  // Cập nhật danh sách sản phẩm
  const itemsBody = orderDetailModal.querySelector('#detail-items');
  itemsBody.innerHTML = '';
  
  order.items.forEach(item => {
    const tr = document.createElement('tr');
    const itemTotal = item.total || (item.price * item.quantity);
    
    tr.innerHTML = `
      <td>
        <div class="item-info">
          <img src="${item.productImg}" alt="${item.productName}" class="item-image">
          <div>
            <div class="item-name">${item.productName}</div>
            ${item.note ? `<div class="item-note">Ghi chú: ${item.note}</div>` : ''}
          </div>
        </div>
      </td>
      <td>${formatPrice(item.price)}</td>
      <td>${item.quantity}</td>
      <td>${formatPrice(itemTotal)}</td>
    `;
    
    itemsBody.appendChild(tr);
  });
  
  // Cập nhật tổng tiền
  orderDetailModal.querySelector('#detail-subtotal').textContent = formatPrice(order.subtotal);
  orderDetailModal.querySelector('#detail-shipping').textContent = formatPrice(order.shipping);
  orderDetailModal.querySelector('#detail-total').textContent = formatPrice(order.total);
  
  // Cập nhật timeline
  const timeline = orderDetailModal.querySelector('#order-timeline');
  timeline.innerHTML = '';
  
  // Tạo timeline cho đơn hàng
  const timelineEvents = [
    { status: 'processing', text: 'Đơn hàng đã được đặt', time: order.orderDate },
    { status: 'confirmed', text: 'Đơn hàng đã được xác nhận', time: order.confirmedAt },
    { status: 'shipping', text: 'Đơn hàng đang được giao', time: order.shippingAt },
    { status: 'delivered', text: 'Đơn hàng đã giao đến nơi', time: order.deliveredAt },
    { status: 'completed', text: 'Đơn hàng đã hoàn thành', time: order.completedAt },
    { status: 'cancelled', text: 'Đơn hàng đã bị hủy', time: order.cancelledAt }
  ];
  
  // Lọc các sự kiện có thời gian
  const filteredEvents = timelineEvents.filter(event => event.time);
  
  // Sắp xếp theo thời gian
  filteredEvents.sort((a, b) => new Date(a.time) - new Date(b.time));
  
  filteredEvents.forEach(event => {
    const timelineItem = document.createElement('div');
    timelineItem.className = 'timeline-item';
    
    timelineItem.innerHTML = `
      <div class="timeline-icon status-${event.status}"></div>
      <div class="timeline-content">
        <div class="timeline-time">${formatDate(event.time)}</div>
        <div class="timeline-text">${event.text}</div>
      </div>
    `;
    
    timeline.appendChild(timelineItem);
  });
  
  // Cập nhật trạng thái trong select box
  const statusSelect = orderDetailModal.querySelector('#admin-status-select');
  statusSelect.value = order.orderStatus;
  
  // Hiển thị modal
  orderDetailModal.style.display = 'block';
}

/**
 * Thiết lập các bộ lọc
 */
function setupFilters() {
  // Cập nhật số lượng đơn hàng theo trạng thái
  updateOrderCounts();
  
  // Xử lý tìm kiếm
  const searchInput = document.getElementById('order-search');
  if (searchInput) {
    searchInput.addEventListener('input', function() {
      const searchText = this.value.toLowerCase().trim();
      filterOrders(searchText);
    });
  }
}

/**
 * Cập nhật số lượng đơn hàng theo trạng thái
 */
/**
 * Thiết lập các nút hành động
 */
function setupActionButtons() {
  // Nút đồng bộ đơn hàng
  const syncOrdersBtn = document.getElementById('sync-orders-btn');
  if (syncOrdersBtn) {
    syncOrdersBtn.addEventListener('click', function() {
      // Thêm lớp loading khi đang đồng bộ
      this.classList.add('loading');
      this.disabled = true;
      
      // Gọi hàm đồng bộ
      syncOrders()
        .then(() => {
          // Xóa lớp loading khi hoàn thành
          this.classList.remove('loading');
          this.disabled = false;
        })
        .catch(error => {
          console.error('Lỗi khi đồng bộ đơn hàng:', error);
          // Xóa lớp loading khi có lỗi
          this.classList.remove('loading');
          this.disabled = false;
          // Hiển thị thông báo lỗi
          showNotification('Đồng bộ thất bại: ' + error.message, 'error');
        });
    });
  }
  
  // Nút tải lại danh sách đơn hàng
  const refreshOrdersBtn = document.getElementById('refresh-orders-btn');
  if (refreshOrdersBtn) {
    refreshOrdersBtn.addEventListener('click', function() {
      // Thêm lớp loading khi đang tải lại
      this.classList.add('loading');
      this.disabled = true;
      
      // Gọi hàm tải lại
      loadOrders();
      
      // Sau 1 giây sẽ cập nhật trạng thái nút
      setTimeout(() => {
        // Xóa lớp loading
        this.classList.remove('loading');
        this.disabled = false;
        // Hiển thị thông báo
        showNotification('Đã tải lại danh sách đơn hàng', 'success');
      }, 1000);
    });
  }
}

function updateOrderCounts() {
  // Lấy dữ liệu đơn hàng từ tất cả các nguồn
  const localOrders = JSON.parse(localStorage.getItem('orders')) || [];
  const orderHistory = JSON.parse(localStorage.getItem('orderHistory')) || [];
  const cachedOrders = JSON.parse(localStorage.getItem('allOrdersCache')) || [];
  
  // Gộp và loại bỏ trùng lặp
  const allOrdersMap = new Map();
  
  [...cachedOrders, ...localOrders, ...orderHistory].forEach(order => {
    allOrdersMap.set(order.id, order);
  });
  
  const orders = Array.from(allOrdersMap.values());
  
  // Đếm số lượng đơn hàng theo trạng thái
  const counts = {
    all: orders.length,
    processing: orders.filter(order => order.orderStatus === 'processing' || !order.orderStatus).length,
    confirmed: orders.filter(order => order.orderStatus === 'confirmed').length,
    shipping: orders.filter(order => order.orderStatus === 'shipping').length,
    delivered: orders.filter(order => order.orderStatus === 'delivered').length,
    completed: orders.filter(order => order.orderStatus === 'completed').length,
    cancelled: orders.filter(order => order.orderStatus === 'cancelled').length
  };
  
  // Cập nhật số lượng hiển thị
  document.querySelectorAll('.order-count').forEach(element => {
    const status = element.getAttribute('data-status');
    if (counts[status] !== undefined) {
      element.textContent = counts[status];
    }
  });
}

/**
 * Lọc đơn hàng theo trạng thái
 */
function filterOrdersByStatus(status) {
  // Cập nhật active trên bộ lọc
  document.querySelectorAll('.filter-status').forEach(element => {
    if (element.getAttribute('data-status') === status) {
      element.classList.add('active');
    } else {
      element.classList.remove('active');
    }
  });
  
  // Tải đơn hàng từ localStorage
  const orders = JSON.parse(localStorage.getItem('orders')) || [];
  
  // Lọc theo trạng thái
  let filteredOrders = orders;
  if (status !== 'all') {
    filteredOrders = orders.filter(order => order.orderStatus === status);
  }
  
  // Hiển thị đơn hàng đã lọc
  displayOrders(filteredOrders);
}

/**
 * Lọc đơn hàng theo từ khóa tìm kiếm
 */
function filterOrders(searchText) {
  if (!searchText) {
    // Nếu không có từ khóa tìm kiếm, hiển thị tất cả đơn hàng
    const activeFilter = document.querySelector('.filter-status.active');
    if (activeFilter) {
      const status = activeFilter.getAttribute('data-status');
      filterOrdersByStatus(status);
    } else {
      loadOrders();
    }
    return;
  }
  
  // Tải đơn hàng từ localStorage
  const orders = JSON.parse(localStorage.getItem('orders')) || [];
  
  // Lọc theo từ khóa tìm kiếm
  const filteredOrders = orders.filter(order => {
    return (
      order.id.toLowerCase().includes(searchText) ||
      order.customerName.toLowerCase().includes(searchText) ||
      (order.username && order.username.toLowerCase().includes(searchText)) ||
      order.phone.includes(searchText) ||
      order.address.toLowerCase().includes(searchText)
    );
  });
  
  // Hiển thị đơn hàng đã lọc
  displayOrders(filteredOrders);
}

/**
 * Xóa đơn hàng
 */
function deleteOrder(orderId) {
  if (!confirm('Bạn có chắc chắn muốn xóa đơn hàng này?')) {
    return;
  }
  
  // Xóa từ localStorage
  const orders = JSON.parse(localStorage.getItem('orders')) || [];
  const updatedOrders = orders.filter(order => order.id !== orderId);
  localStorage.setItem('orders', JSON.stringify(updatedOrders));
  
  // Xóa từ lịch sử đơn hàng
  const orderHistory = JSON.parse(localStorage.getItem('orderHistory')) || [];
  const updatedHistory = orderHistory.filter(order => order.id !== orderId);
  localStorage.setItem('orderHistory', JSON.stringify(updatedHistory));
  
  // Cập nhật giao diện
  const orderRow = document.querySelector(`tr[data-order-id="${orderId}"]`);
  if (orderRow) {
    orderRow.remove();
  }
  
  // Cập nhật số lượng đơn hàng
  updateOrderCounts();
  
  // Hiển thị thông báo
  showNotification('Đã xóa đơn hàng thành công');
  
  // Xóa từ API
  deleteOrderFromApi(orderId);
}

/**
 * Xóa đơn hàng từ API
 */
function deleteOrderFromApi(orderId) {
  fetch(`${ORDERS_API}/${orderId}`, {
    method: 'DELETE'
  })
  .then(response => {
    if (!response.ok) {
      throw new Error('Không thể xóa đơn hàng từ API');
    }
    console.log('Đã xóa đơn hàng từ API thành công');
  })
  .catch(error => {
    console.error('Lỗi khi xóa đơn hàng từ API:', error);
  });
}

/**
 * Hiển thị giao diện quản lý đơn hàng
 */
function showOrderManagement() {
  // Ẩn các tab khác
  const contentSections = document.querySelectorAll('.content-section');
  contentSections.forEach(section => {
    section.style.display = 'none';
  });
  
  // Hiển thị tab quản lý đơn hàng
  const orderSection = document.getElementById('order-management');
  if (orderSection) {
    orderSection.style.display = 'block';
  } else {
    // Nếu chưa có giao diện quản lý đơn hàng, tạo mới
    createOrderManagementUI();
  }
  
  // Cập nhật active menu
  const menuItems = document.querySelectorAll('.menu-item');
  menuItems.forEach(item => {
    item.classList.remove('active');
  });
  
  const orderMenu = document.getElementById('order-management-menu');
  if (orderMenu) {
    orderMenu.parentElement.classList.add('active');
  }
}

/**
 * Tạo giao diện quản lý đơn hàng
 */
function createOrderManagementUI() {
  const contentContainer = document.querySelector('.admin-content') || document.querySelector('.main-content');
  if (!contentContainer) return;
  
  const orderSection = document.createElement('div');
  orderSection.id = 'order-management';
  orderSection.className = 'content-section';
  
  orderSection.innerHTML = `
    <div class="section-header">
      <h2>Quản lý đơn hàng</h2>
      <div class="section-actions">
        <div class="search-box">
          <input type="text" id="order-search" placeholder="Tìm kiếm đơn hàng...">
          <i class="fas fa-search"></i>
        </div>
      </div>
    </div>
    
    <div class="filter-tabs">
      <div class="filter-tab filter-status active" data-status="all">
        Tất cả <span class="order-count" data-status="all">0</span>
      </div>
      <div class="filter-tab filter-status" data-status="processing">
        Đang xử lý <span class="order-count" data-status="processing">0</span>
      </div>
      <div class="filter-tab filter-status" data-status="confirmed">
        Đã xác nhận <span class="order-count" data-status="confirmed">0</span>
      </div>
      <div class="filter-tab filter-status" data-status="shipping">
        Đang giao <span class="order-count" data-status="shipping">0</span>
      </div>
      <div class="filter-tab filter-status" data-status="completed">
        Hoàn thành <span class="order-count" data-status="completed">0</span>
      </div>
      <div class="filter-tab filter-status" data-status="cancelled">
        Đã hủy <span class="order-count" data-status="cancelled">0</span>
      </div>
    </div>
    
    <div class="table-responsive">
      <table class="admin-table orders-table">
        <thead>
          <tr>
            <th>Mã đơn hàng</th>
            <th>Khách hàng</th>
            <th>Ngày đặt</th>
            <th>Tổng tiền</th>
            <th>Trạng thái</th>
            <th>Cập nhật</th>
            <th>Thao tác</th>
          </tr>
        </thead>
        <tbody id="order-table-body">
          <!-- Danh sách đơn hàng sẽ được thêm vào đây -->
        </tbody>
      </table>
    </div>
  `;
  
  contentContainer.appendChild(orderSection);
  
  // Tải đơn hàng
  loadOrders();
}

/**
 * Hiển thị thông báo
 */
function showNotification(message, type = 'success') {
  // Kiểm tra có hàm showToast từ file khác không
  if (typeof window.showToast === 'function') {
    window.showToast(message);
    return;
  }
  
  // Tạo thông báo mới nếu không có hàm showToast
  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.innerHTML = `
    <div class="notification-content">
      <span>${message}</span>
    </div>
    <span class="notification-close">&times;</span>
  `;
  
  document.body.appendChild(notification);
  
  // Hiển thị thông báo
  setTimeout(() => {
    notification.classList.add('show');
  }, 100);
  
  // Xử lý sự kiện đóng thông báo
  notification.querySelector('.notification-close').addEventListener('click', function() {
    notification.classList.remove('show');
    setTimeout(() => {
      notification.remove();
    }, 300);
  });
  
  // Tự động ẩn thông báo sau 5 giây
  setTimeout(() => {
    notification.classList.remove('show');
    setTimeout(() => {
      notification.remove();
    }, 300);
  }, 5000);
}

/**
 * Định dạng ngày tháng
 */
function formatDate(dateString) {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('vi-VN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
}

/**
 * Định dạng giá tiền
 */
function formatPrice(price) {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(price);
}

/**
 * Lấy tên phương thức thanh toán
 */
function getPaymentMethodText(method) {
  switch(method) {
    case 'cash': return 'Thanh toán khi nhận hàng';
    case 'bank': return 'Chuyển khoản ngân hàng';
    case 'momo': return 'Ví MoMo';
    case 'credit': return 'Thẻ tín dụng/ghi nợ';
    default: return 'Thanh toán khi nhận hàng';
  }
}

/**
 * Lấy tên trạng thái đơn hàng
 */
function getOrderStatusText(statusCode) {
  switch(statusCode) {
    case 'processing': return 'Đang xử lý';
    case 'confirmed': return 'Đã xác nhận';
    case 'shipping': return 'Đang giao hàng';
    case 'delivered': return 'Đã giao hàng';
    case 'completed': return 'Hoàn thành';
    case 'cancelled': return 'Đã hủy';
    default: return 'Đang xử lý';
  }
}

// Đồng bộ đơn hàng từ localStorage lên API khi tải trang
document.addEventListener('DOMContentLoaded', function() {
  // Kiểm tra xem đã có đơn hàng nào chưa được đồng bộ không
  const orderHistory = JSON.parse(localStorage.getItem('orderHistory')) || [];
  const unsyncedOrders = orderHistory.filter(order => !order.synced);
  
  if (unsyncedOrders.length > 0) {
    console.log(`Có ${unsyncedOrders.length} đơn hàng chưa được đồng bộ`);
    
    // Đồng bộ các đơn hàng
    fetch(API_BASE_URL)
      .then(response => {
        if (response.ok) {
          // Có kết nối đến API, tiến hành đồng bộ
          unsyncedOrders.forEach(order => {
            fetch(ORDERS_API, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(order)
            })
            .then(response => response.json())
            .then(data => {
              console.log('Đồng bộ đơn hàng thành công:', data);
              
              // Đánh dấu đơn hàng đã được đồng bộ
              const orderHistory = JSON.parse(localStorage.getItem('orderHistory')) || [];
              const updatedHistory = orderHistory.map(item => {
                if (item.id === order.id) {
                  return { ...item, synced: true };
                }
                return item;
              });
              localStorage.setItem('orderHistory', JSON.stringify(updatedHistory));
            })
            .catch(error => {
              console.error('Lỗi khi đồng bộ đơn hàng:', error);
            });
          });
        } else {
          console.warn('Không thể kết nối đến API để đồng bộ đơn hàng');
        }
      })
      .catch(error => {
        console.error('Lỗi khi kiểm tra kết nối API:', error);
      });
  }
});