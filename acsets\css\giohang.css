/* Overlay - chỉ làm mờ nền phía sau */

/* Cart Container - luôn hiển thị rõ nét */
.cart-container {
  position: fixed;
  top: 20px; right: 20px;
  width: 350px;
  height: calc(100% - 40px);
  background: #fff; /* N<PERSON>n trắng để đảm bảo rõ nét */
  padding: 24px;
  border-radius: 16px;
  box-shadow: -5px 0 25px rgba(0, 0, 0, 0.2);
  transform: translateX(100%);
  opacity: 0;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  transition: transform 0.4s cubic-bezier(0.22, 1, 0.36, 1), opacity 0.3s ease;
  z-index: 1001; /* <PERSON> hơn overlay để hiển thị trên cùng */
}
.cart-container.active {
  transform: translateX(0);
  opacity: 1;
}

/* Header */
.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15px;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
}
.cart-header h2 {
  font-size: 22px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

/* Close Button */
.close-btn {
  width: 36px;
  height: 36px;
  font-size: 22px;
  border: none;
  background: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}
.close-btn:hover {
  background: rgba(185, 32, 37, 0.1);
  color: #b92025;
}

/* Empty State */
.cart-empty {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #777;
  text-align: center;
}
.cart-icon {
  font-size: 60px;
  margin-bottom: 16px;
  color: #ccc;
  opacity: 0.8;
}
.cart-empty .empty-title {
  font-size: 18px;
  font-weight: 600;
  color: #555;
  margin-bottom: 10px;
}
.cart-empty p {
  font-size: 15px;
  text-align: center;
  max-width: 240px;
  margin: 5px 0;
}

/* Cart Empty Icon */
.cart-empty-icon {
  font-size: 48px;
  color: #555;
  position: relative;
  display: inline-block;
  margin-bottom: 16px;
}

/* Font Awesome icons - đảm bảo thư viện Font Awesome được tải */
.cart-empty-icon::before {
  content: "\f07a"; /* icon giỏ hàng */
  font-family: "Font Awesome 6 Free", FontAwesome, "Font Awesome 5 Free";
  font-weight: 900;
}

.cart-empty-icon::after {
  content: "\f00d"; /* icon dấu X */
  font-family: "Font Awesome 6 Free", FontAwesome, "Font Awesome 5 Free";
  font-weight: 900;
  font-size: 20px;
  color: #d00;
  position: absolute;
  top: 0;
  right: 0;
}

/* Cart Items */
.cart-items {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 20px;
  padding-right: 5px;
}
.cart-items::-webkit-scrollbar {
  width: 5px;
}
.cart-items::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 10px;
}

.cart-item {
  display: flex;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}
.cart-item-image {
  width: 75px;
  height: 75px;
  object-fit: cover;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.08);
  margin-right: 15px;
}
.cart-item-details {
  flex: 1;
}
.cart-item-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}
.cart-item-price {
  font-size: 16px;
  font-weight: 600;
  color: #b92025;
  margin: 5px 0;
}
.cart-item-remove {
  position: absolute;
  top: 15px;
  right: 0;
  font-size: 18px;
  color: #999;
  background: none;
  border: none;
  cursor: pointer;
  transition: color 0.2s;
}
.cart-item-remove:hover {
  color: #b92025;
}

/* Quantity Controls */
.cart-item-quantity {
  display: flex;
  align-items: center;
  margin-top: 10px;
}
.quantity-btn {
  width: 30px; height: 30px;
  font-size: 16px;
  background: #f5f5f5;
  color: #555;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
}
.quantity-btn:hover {
  background: #e0e0e0;
}
.quantity-value {
  padding: 0 12px;
  font-weight: 500;
  color: #333;
}

/* Cart Footer */
.cart-footer {
  margin-top: auto;
  padding-top: 20px;
  border-top: 1px solid #eee;
}
.cart-footer .subtotal,
.cart-footer .shipping,
.cart-footer .total {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  color: #666;
}
.cart-footer .shipping-value {
  color: #00a650;
  font-weight: 500;
}
.cart-footer .total {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin: 15px 0;
}

/* Buttons */
.buttons {
  display: flex;
  gap: 12px;
}
.buttons button {
  flex: 1;
  padding: 14px;
  font-size: 15px;
  font-weight: 600;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
}
.add-more {
  background: #f0f0f0;
  color: #333;
}
.add-more:hover {
  background: #e0e0e0;
  transform: translateY(-2px);
}
.checkout {
  background: #b92025;
  color: #fff;
  box-shadow: 0 4px 10px rgba(185, 32, 37, 0.25);
}
.checkout:hover {
  background: #a51c20;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(185, 32, 37, 0.35);
}
.checkout:active {
  transform: translateY(0);
}

/* Promo Code */
.promo-code {
  display: flex;
  gap: 8px;
  margin: 15px 0;
}
.promo-code input {
  flex: 1;
  padding: 10px 12px;
  font-size: 14px;
  border: 1px solid #ddd;
  border-radius: 8px;
}
.promo-code button {
  background: #333;
  color: #fff;
  padding: 0 15px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.2s;
}
.promo-code button:hover {
  background: #222;
}

/* Responsive */
@media (max-width: 480px) {
  .cart-container {
    width: calc(100% - 40px);
    padding: 20px;
    border-radius: 12px;
  }
  .cart-header h2 {
    font-size: 20px;
  }
  .buttons button {
    padding: 12px;
  }
}