// Admin Panel JavaScript - Save this to ./js/admin.js

document.addEventListener('DOMContentLoaded', function() {
    // Initialize localStorage if first time
    initializeLocalStorage();
    
    // Sidebar navigation
    const sidebarItems = document.querySelectorAll('.sidebar-item');
    const pages = document.querySelectorAll('.page-content');

    sidebarItems.forEach(item => {
        item.addEventListener('click', function() {
            const target = this.getAttribute('data-page');
            if (!target) return;

            // Update sidebar active state
            sidebarItems.forEach(i => i.classList.remove('active'));
            this.classList.add('active');

            // Show the selected page
            pages.forEach(page => {
                if (page.classList.contains(target + '-page')) {
                    page.classList.add('active');
                } else {
                    page.classList.remove('active');
                }
            });

            // Update header title
            const headerTitle = document.querySelector('.header-left h2');
            if (headerTitle) {
                const spanText = this.querySelector('span');
                headerTitle.textContent = spanText ? spanText.textContent : '';
            }
        });
    });

    // Toggle sidebar on mobile
    const toggleSidebarBtn = document.getElementById('toggle-sidebar');
    const sidebar = document.querySelector('.sidebar');
    const mainContent = document.querySelector('.main-content');

    if (toggleSidebarBtn && sidebar && mainContent) {
        toggleSidebarBtn.addEventListener('click', function() {
            sidebar.classList.toggle('active');
            mainContent.classList.toggle('full-width');
        });
    }

    // Add Product Modal
    const addProductBtn = document.getElementById('add-product-btn');
    const addProductModal = document.getElementById('add-product-modal');
    const closeModalBtns = document.querySelectorAll('.close-modal, .cancel-btn');

    // Open modal
    if (addProductBtn && addProductModal) {
        addProductBtn.addEventListener('click', function() {
            addProductModal.style.display = 'block';
        });
    }

    // Close modals
    closeModalBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                modal.style.display = 'none';
            });
        });
    });

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
            event.target.style.display = 'none';
        }
    });

    // Image Preview
    const productImageInput = document.getElementById('product-image');
    const imagePreview = document.querySelector('.image-preview');

    if (productImageInput && imagePreview) {
        productImageInput.addEventListener('change', function(e) {
            if (e.target.files && e.target.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Create image element
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    
                    // Clear preview and add new image
                    imagePreview.innerHTML = '';
                    imagePreview.appendChild(img);
                }
                reader.readAsDataURL(e.target.files[0]);
            }
        });
    }

    // Add Product Form Submission
    const addProductForm = document.getElementById('add-product-form');
    
    if (addProductForm) {
        addProductForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form values
            const name = document.getElementById('product-name').value;
            const category = document.getElementById('product-category').value;
            const price = document.getElementById('product-price').value;
            const description = document.getElementById('product-description').value;
            
            const statusRadio = document.querySelector('input[name="status"]:checked');
            const status = statusRadio ? statusRadio.value : 'active';
            
            // Validate form
            if (!name || !category || !price) {
                showNotification('Vui lòng điền đầy đủ thông tin bắt buộc!', 'error');
                return;
            }
            
            // Create product object
            const product = {
                id: generateId(),
                name,
                category,
                price: parseFloat(price) || 0,
                description,
                status,
                image: imagePreview && imagePreview.querySelector('img') ? 
                       imagePreview.querySelector('img').src : null
            };
            
            // Add to products array
            addProduct(product);
            
            // Reset form and close modal
            addProductForm.reset();
            if (imagePreview) imagePreview.innerHTML = '';
            if (addProductModal) addProductModal.style.display = 'none';
            
            // Show success message
            showNotification('Sản phẩm đã được thêm thành công!', 'success');
            
            // Refresh products list
            loadProducts();
        });
    }
    
    // Add User Form Submission (if exists)
    const addUserForm = document.getElementById('add-user-form');
    if (addUserForm) {
        addUserForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form values
            const name = document.getElementById('user-name').value;
            const email = document.getElementById('user-email').value;
            const phone = document.getElementById('user-phone').value;
            const role = document.getElementById('user-role').value;
            
            // Validate form
            if (!name || !email || !phone) {
                showNotification('Vui lòng điền đầy đủ thông tin bắt buộc!', 'error');
                return;
            }
            
            // Create user object
            const user = {
                id: generateId(),
                name,
                email,
                phone,
                role: role || 'user',
                status: 'active'
            };
            
            // Add to users array
            users.push(user);
            
            // Save to localStorage
            saveToLocalStorage('nafood_users', users);
            
            // Reset form and close modal
            addUserForm.reset();
            const addUserModal = document.getElementById('add-user-modal');
            if (addUserModal) addUserModal.style.display = 'none';
            
            // Show success message
            showNotification('Tài khoản đã được tạo thành công!', 'success');
            
            // Refresh users list
            loadUsers();
        });
    }

    // Initialize Charts if Chart.js is available
    if (typeof Chart !== 'undefined') {
        // Revenue Chart
        if (document.getElementById('revenue-chart')) {
            const revenueCtx = document.getElementById('revenue-chart').getContext('2d');
            const revenueChart = new Chart(revenueCtx, {
                type: 'line',
                data: {
                    labels: ['Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6'],
                    datasets: [{
                        label: 'Doanh thu (triệu VNĐ)',
                        data: [12, 19, 15, 22, 25, 30],
                        backgroundColor: 'rgba(255, 107, 107, 0.2)',
                        borderColor: 'rgba(255, 107, 107, 1)',
                        borderWidth: 2,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // Product Chart
        if (document.getElementById('product-chart')) {
            const productCtx = document.getElementById('product-chart').getContext('2d');
            const productChart = new Chart(productCtx, {
                type: 'bar',
                data: {
                    labels: ['Bánh mì', 'Phở bò', 'Bún chả', 'Cơm tấm', 'Trà sữa'],
                    datasets: [{
                        label: 'Số lượng bán',
                        data: [120, 90, 80, 75, 65],
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(153, 102, 255, 0.7)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    } else {
        // Chart.js not loaded warning
        console.warn('Chart.js is not loaded. Charts will not be displayed.');
    }

    // Initial data load
    loadProducts();
    loadOrders();
    loadUsers();

    // Show order details when clicking on an order
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('view-order-btn') || 
            e.target.closest('.view-order-btn')) {
            
            const orderBtn = e.target.closest('.view-order-btn');
            if (orderBtn && orderBtn.dataset.id) {
                openOrderDetails(orderBtn.dataset.id);
            }
        }
    });

    // Handle product actions (edit, delete)
    document.addEventListener('click', function(e) {
        // Edit product
        if (e.target.classList.contains('edit-btn') || 
            e.target.closest('.edit-btn')) {
            
            const btn = e.target.closest('.edit-btn');
            if (btn && btn.dataset.id) {
                editProduct(btn.dataset.id);
            }
        }
        
        // Delete product
        if (e.target.classList.contains('delete-btn') || 
            e.target.closest('.delete-btn')) {
            
            const btn = e.target.closest('.delete-btn');
            if (btn && btn.dataset.id) {
                deleteProduct(btn.dataset.id);
            }
        }
        
        // Block/Unblock user
        if (e.target.classList.contains('block-btn') || 
            e.target.closest('.block-btn')) {
            
            const btn = e.target.closest('.block-btn');
            if (btn && btn.dataset.id) {
                toggleUserStatus(btn.dataset.id, 'blocked');
            }
        }
        
        if (e.target.classList.contains('unblock-btn') || 
            e.target.closest('.unblock-btn')) {
            
            const btn = e.target.closest('.unblock-btn');
            if (btn && btn.dataset.id) {
                toggleUserStatus(btn.dataset.id, 'active');
            }
        }
    });
});

// Data management using localStorage
// Kiểm tra nếu có dữ liệu trong localStorage
let data = JSON.parse(localStorage.getItem('data'));

// Nếu có dữ liệu, lấy danh sách sản phẩm
if (data && data.products) {
    const products = data.products;
    console.log(products);  // In ra danh sách sản phẩm
} else {
    console.log('Không có dữ liệu sản phẩm trong localStorage');
}

// In a real application, these would be API calls

// Default data (used when localStorage is empty)

const defaultOrders = [
    {
        id: 'o1',
        customer: {
            name: 'Nguyen Van A',
            phone: '0123456789',
            address: '123 Đường ABC, Phường XYZ, TP Trà Vinh'
        },
        items: [
            { product: 'Bánh mì thịt', quantity: 2, price: 25000 },
            { product: 'Nước mía', quantity: 1, price: 15000 }
        ],
        total: 65000,
        date: '2023-05-01',
        status: 'pending',
        paymentMethod: 'Tiền mặt'
    },
    {
        id: 'o2',
        customer: {
            name: 'Tran Thi B',
            phone: '0987654321',
            address: '456 Đường DEF, Phường UVW, TP Trà Vinh'
        },
        items: [
            { product: 'Phở bò', quantity: 3, price: 45000 },
            { product: 'Nước mía', quantity: 2, price: 15000 }
        ],
        total: 165000,
        date: '2023-05-02',
        status: 'delivered',
        paymentMethod: 'Tiền mặt'
    }
];

// Load data from localStorage or use defaults
let products = loadFromLocalStorage('nafood_products', defaultProducts);
let users = loadFromLocalStorage('nafood_users', defaultUsers);
let orders = loadFromLocalStorage('nafood_orders', defaultOrders);

// Helper function to load data from localStorage
function loadFromLocalStorage(key, defaultData) {
    try {
        const localData = localStorage.getItem(key);
        return localData ? JSON.parse(localData) : defaultData;
    } catch (error) {
        console.error(`Error loading ${key} from localStorage:`, error);
        return defaultData;
    }
}

// Helper function to save data to localStorage
function saveToLocalStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
        return true;
    } catch (error) {
        console.error(`Error saving ${key} to localStorage:`, error);
        return false;
    }
}

// Function to generate a random ID
function generateId() {
    return Math.random().toString(36).substr(2, 9);
}

// Function to add a product
function addProduct(product) {
    products.push(product);
    
    // Save to localStorage
    saveToLocalStorage('nafood_products', products);
    
    // In a real app, save to backend
    // fetch('/api/products', {
    //     method: 'POST',
    //     headers: {
    //         'Content-Type': 'application/json',
    //     },
    //     body: JSON.stringify(product),
    // });
}

// Function to edit a product
function editProduct(productId) {
    const product = products.find(p => p.id === productId);
    if (!product) {
        showNotification('Không tìm thấy sản phẩm!', 'error');
        return;
    }
    
    // Get edit product modal and form
    const editProductModal = document.getElementById('edit-product-modal');
    const editProductForm = document.getElementById('edit-product-form');
    
    if (editProductModal && editProductForm) {
        // Populate form fields with product data
        document.getElementById('edit-product-id').value = product.id;
        document.getElementById('edit-product-name').value = product.name;
        document.getElementById('edit-product-category').value = product.category;
        document.getElementById('edit-product-price').value = product.price;
        document.getElementById('edit-product-description').value = product.description;
        
        // Set status radio button
        const statusRadio = document.querySelector(`input[name="edit-status"][value="${product.status}"]`);
        if (statusRadio) statusRadio.checked = true;
        
        // Set image preview if exists
        const editImagePreview = document.querySelector('.edit-image-preview');
        if (editImagePreview && product.image) {
            const img = document.createElement('img');
            img.src = product.image;
            editImagePreview.innerHTML = '';
            editImagePreview.appendChild(img);
        }
        
        // Show modal
        editProductModal.style.display = 'block';
        
        // Set up form submission
        editProductForm.onsubmit = function(e) {
            e.preventDefault();
            
            // Update product with new values
            product.name = document.getElementById('edit-product-name').value;
            product.category = document.getElementById('edit-product-category').value;
            product.price = parseFloat(document.getElementById('edit-product-price').value) || 0;
            product.description = document.getElementById('edit-product-description').value;
            
            const statusRadio = document.querySelector('input[name="edit-status"]:checked');
            product.status = statusRadio ? statusRadio.value : 'active';
            
            // Update image if changed
            if (editImagePreview && editImagePreview.querySelector('img')) {
                product.image = editImagePreview.querySelector('img').src;
            }
            
            // Save to localStorage
            saveToLocalStorage('nafood_products', products);
            
            // Refresh products list
            loadProducts();
            
            // Close modal and show success message
            editProductModal.style.display = 'none';
            showNotification('Sản phẩm đã được cập nhật thành công!', 'success');
        };
    } else {
        // If modal or form doesn't exist, show notification
        showNotification('Chức năng chỉnh sửa sản phẩm đang được phát triển!', 'info');
    }
}

// Function to delete a product
function deleteProduct(productId) {
    // Find product index
    const productIndex = products.findIndex(p => p.id === productId);
    if (productIndex === -1) {
        showNotification('Không tìm thấy sản phẩm!', 'error');
        return;
    }
    
    // Confirm deletion
    if (confirm('Bạn có chắc chắn muốn xóa sản phẩm này?')) {
        // Remove from array
        products.splice(productIndex, 1);
        
        // Save to localStorage
        saveToLocalStorage('nafood_products', products);
        
        // In a real app, delete from backend
        // fetch(`/api/products/${productId}`, {
        //     method: 'DELETE'
        // });
        
        // Refresh products list
        loadProducts();
        
        // Show success message
        showNotification('Sản phẩm đã được xóa thành công!', 'success');
    }
}

// Function to toggle user status
function toggleUserStatus(userId, newStatus) {
    const user = users.find(u => u.id === userId);
    if (!user) {
        showNotification('Không tìm thấy người dùng!', 'error');
        return;
    }
    
    // Update status
    user.status = newStatus;
    
    // Save to localStorage
    saveToLocalStorage('nafood_users', users);
    
    // In a real app, update on backend
    // fetch(`/api/users/${userId}`, {
    //     method: 'PATCH',
    //     headers: {
    //         'Content-Type': 'application/json',
    //     },
    //     body: JSON.stringify({ status: newStatus }),
    // });
    
    // Refresh users list
    loadUsers();
    
    // Show success message
    const message = newStatus === 'active' ? 'Đã mở khóa tài khoản!' : 'Đã khóa tài khoản!';
    showNotification(message, 'success');
}

// Function to load products
function loadProducts() {
    const productsListBody = document.getElementById('products-list-body');
    
    if (!productsListBody) return;
    
    productsListBody.innerHTML = '';
    
    products.forEach(product => {
        const row = document.createElement('tr');
        
        // Handle potential image path errors
        let imagePath = product.image || './assets/image/placeholder.jpg';
        // Ensure image path is using the correct format
        imagePath = imagePath.replace('./acsets/image/', './assets/image/');
        
        row.innerHTML = `
            <td>${product.id}</td>
            <td>
                <img src="${imagePath}" 
                     alt="${product.name}" 
                     style="width: 50px; height: 50px; object-fit: cover; border-radius: 5px;"
                     onerror="this.src='./assets/image/placeholder.jpg';">
            </td>
            <td>${product.name}</td>
            <td>${getCategoryName(product.category)}</td>
            <td>${formatCurrency(product.price)}</td>
            <td>
                <span class="status ${product.status === 'active' ? 'delivered' : 'cancelled'}">
                    ${product.status === 'active' ? 'Hoạt động' : 'Ẩn'}
                </span>
            </td>
            <td>
                <button class="action-btn edit-btn" data-id="${product.id}">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn delete-btn" data-id="${product.id}">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        
        productsListBody.appendChild(row);
    });
}

// Function to load orders
function loadOrders() {
    const ordersListBody = document.getElementById('orders-list-body');
    
    if (!ordersListBody) return;
    
    ordersListBody.innerHTML = '';
    
    orders.forEach(order => {
        const row = document.createElement('tr');
        
        row.innerHTML = `
            <td>${order.id}</td>
            <td>${order.customer.name}</td>
            <td>${order.customer.phone}</td>
            <td>${order.items.map(item => item.product).join(', ')}</td>
            <td>${formatCurrency(order.total)}</td>
            <td>${formatDate(order.date)}</td>
            <td>
                <span class="status ${order.status}">
                    ${getOrderStatusName(order.status)}
                </span>
            </td>
            <td>
                <button class="action-btn view-order-btn" data-id="${order.id}">
                    <i class="fas fa-eye"></i>
                </button>
            </td>
        `;
        
        ordersListBody.appendChild(row);
    });
}

// Function to load users
function loadUsers() {
    const usersListBody = document.getElementById('users-list-body');
    
    if (!usersListBody) return;
    
    usersListBody.innerHTML = '';
    
    users.forEach(user => {
        const row = document.createElement('tr');
        
        row.innerHTML = `
            <td>${user.id}</td>
            <td>${user.name}</td>
            <td>${user.email}</td>
            <td>${user.phone}</td>
            <td>${user.role === 'admin' ? 'Quản trị viên' : 'Khách hàng'}</td>
            <td>
                <span class="status ${user.status === 'active' ? 'delivered' : 'cancelled'}">
                    ${user.status === 'active' ? 'Hoạt động' : 'Bị khóa'}
                </span>
            </td>
            <td>
                <button class="action-btn edit-btn" data-id="${user.id}">
                    <i class="fas fa-edit"></i>
                </button>
                ${user.role !== 'admin' ? `
                <button class="action-btn ${user.status === 'active' ? 'block-btn' : 'unblock-btn'}" data-id="${user.id}">
                    <i class="fas fa-${user.status === 'active' ? 'ban' : 'unlock'}"></i>
                </button>` : ''}
            </td>
        `;
        
        usersListBody.appendChild(row);
    });
}

// Function to open order details
function openOrderDetails(orderId) {
    const orderModal = document.getElementById('order-detail-modal');
    const order = orders.find(o => o.id === orderId);
    
    if (!order || !orderModal) {
        console.error('Order or modal not found');
        return;
    }
    
    // Set order details
    const elements = {
        'order-id': order.id,
        'order-date': formatDate(order.date),
        'order-status': getOrderStatusName(order.status),
        'payment-method': order.paymentMethod,
        'customer-name': order.customer.name,
        'customer-phone': order.customer.phone,
        'customer-address': order.customer.address,
        'order-total': formatCurrency(order.total)
    };
    
    // Update each element if it exists
    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) element.textContent = value;
    });
    
    // Set order items
    const itemsBody = document.getElementById('order-items-body');
    if (itemsBody) {
        itemsBody.innerHTML = '';
        
        order.items.forEach(item => {
            const row = document.createElement('tr');
            
            row.innerHTML = `
                <td>${item.product}</td>
                <td>${item.quantity}</td>
                <td>${formatCurrency(item.price)}</td>
                <td>${formatCurrency(item.price * item.quantity)}</td>
            `;
            
            itemsBody.appendChild(row);
        });
    }
    
    // Set active status button
    const statusButtons = document.querySelectorAll('.status-btn');
    statusButtons.forEach(btn => {
        btn.classList.remove('active');
        if (btn.classList.contains(order.status)) {
            btn.classList.add('active');
        }
        
        // Remove any previous event listeners by cloning and replacing
        const newBtn = btn.cloneNode(true);
        btn.parentNode.replaceChild(newBtn, btn);
        
        // Add click event to update order status
        newBtn.addEventListener('click', function() {
            const newStatus = this.classList[1];
            updateOrderStatus(orderId, newStatus);
            
            // Update status display
            const orderStatusElem = document.getElementById('order-status');
            if (orderStatusElem) {
                orderStatusElem.textContent = getOrderStatusName(newStatus);
            }
            
            // Update active button
            statusButtons.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            showNotification('Trạng thái đơn hàng đã được cập nhật!', 'success');
        });
    });
    
    // Show modal
    orderModal.style.display = 'block';
}

// Function to update order status
function updateOrderStatus(orderId, status) {
    const order = orders.find(o => o.id === orderId);
    if (order) {
        order.status = status;
        
        // Save to localStorage
        saveToLocalStorage('nafood_orders', orders);
        
        // In a real app, update on backend
        // fetch(`/api/orders/${orderId}`, {
        //     method: 'PATCH',
        //     headers: {
        //         'Content-Type': 'application/json',
        //     },
        //     body: JSON.stringify({ status }),
        // });
        
        // Refresh orders list
        loadOrders();
    }
}

// Helper function to get category name
function getCategoryName(categoryCode) {
    const categories = {
        'mon-chay': 'Món chay',
        'mon-man': 'Món mặn',
        'mon-lau': 'Món lẩu',
        'an-vat': 'Ăn vặt',
        'nuoc-uong': 'Nước uống'
    };
    
    return categories[categoryCode] || categoryCode;
}

// Helper function to get order status name
function getOrderStatusName(statusCode) {
    const statuses = {
        'pending': 'Đang xử lý',
        'confirmed': 'Đã xác nhận',
        'shipping': 'Đang giao',
        'delivered': 'Đã giao',
        'cancelled': 'Đã hủy'
    };
    
    return statuses[statusCode] || statusCode;
}

// Helper function to format currency
function formatCurrency(amount) {
    try {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(amount);
    } catch (error) {
        console.error('Error formatting currency:', error);
        return amount + ' VND';
    }
}

// Helper function to format date
function formatDate(dateString) {
    try {
        const date = new Date(dateString);
        if (isNaN(date)) {
            return dateString;
        }
        return new Intl.DateTimeFormat('vi-VN').format(date);
    } catch (error) {
        console.error('Error formatting date:', error);
        return dateString;
    }
}

// Initialize localStorage with default data if not present
function initializeLocalStorage() {
    // Check if localStorage is available
    if (typeof(Storage) === "undefined") {
        console.error("localStorage is not supported by your browser. Data will not persist.");
        return;
    }
    
    // Initialize products if not in localStorage
    if (!localStorage.getItem('nafood_products')) {
        saveToLocalStorage('nafood_products', products);
    }
    
    // Initialize users if not in localStorage
    if (!localStorage.getItem('nafood_users')) {
        saveToLocalStorage('nafood_users', users);
    }
    
    // Initialize orders if not in localStorage
    if (!localStorage.getItem('nafood_orders')) {
        saveToLocalStorage('nafood_orders', orders);
    }
}

// Function to show notification
function showNotification(message, type = 'info') {
    try {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close">&times;</button>
        `;
        
        // Add to document
        document.body.appendChild(notification);
        
        // Show notification
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // Auto hide after 3 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
        
        // Close button
        const closeBtn = notification.querySelector('.notification-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                notification.classList.remove('show');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            });
        }
    } catch (error) {
        console.error('Error showing notification:', error);
        alert(message);
    }
}