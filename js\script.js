// Khi click vào nút Lọc -> toggle (ẩn/hiện) khung filter-bar
document.querySelector('.filter-btn').addEventListener('click', function () {
    document.querySelector('.filter-bar').classList.toggle('hidden');
  });
  
  // Khi click nút đóng (X) -> luôn ẩn khung filter-bar
  document.querySelector('.close-btn').addEventListener('click', function () {
    document.querySelector('.filter-bar').classList.add('hidden');
  });
  // L<PERSON>y các phần tử cần dùng
  const filterBtn = document.querySelector('.filter-btn');
  const filterBar = document.querySelector('.filter-bar');
  const closeBtn = document.querySelector('.close-btn');
  const searchBtn = document.querySelector('.search-btn');
  const refreshBtn = document.querySelector('.refresh-btn');
  const sortBtns = document.querySelectorAll('.sort-btn');
  const categorySelect = document.getElementById('category');
  const priceMin = document.getElementById('price-min');
  const priceMax = document.getElementById('price-max');
  
  // Bật / tắt filter bar khi click nút "Lọc"
  filterBtn.addEventListener('click', () => {
    filterBar.classList.toggle('hidden');
  });
  
  // Đóng filter bar
  closeBtn.addEventListener('click', () => {
    filterBar.classList.add('hidden');
  });
  
  // Xử lý nút tìm kiếm theo giá và loại
  searchBtn.addEventListener('click', async () => {
    const category = categorySelect.value;
    const min = parseInt(priceMin.value) || 0;
    const max = parseInt(priceMax.value) || Infinity;
  
    console.log('Tìm kiếm với:', { category, min, max });
  
    // Lọc sản phẩm từ MongoDB
    try {
      const filters = {
        category: category || undefined,
        minPrice: min,
        maxPrice: max === Infinity ? undefined : max
      };
      
      await filterProducts(filters);
    } catch (error) {
      console.error('Lỗi khi lọc sản phẩm:', error);
    }
  });
  
  // Làm mới bộ lọc
  refreshBtn.addEventListener('click', async () => {
    categorySelect.selectedIndex = 0;
    priceMin.value = '';
    priceMax.value = '';
    console.log('Đã làm mới bộ lọc');
    
    // Tải lại tất cả sản phẩm
    await loadAllProducts();
  });
  
  // Sắp xếp tăng / giảm giá
  sortBtns.forEach((btn, index) => {
    btn.addEventListener('click', async () => {
      const sortType = index === 0 ? 'asc' : 'desc';
      console.log('Sắp xếp theo giá:', sortType);
  
      // Sắp xếp sản phẩm từ MongoDB
      try {
        await sortProducts(sortType);
      } catch (error) {
        console.error('Lỗi khi sắp xếp sản phẩm:', error);
      }
    });
  });
  
  function toggleFilter() {
    const filterBar = document.querySelector('.filter-bar');
    filterBar.classList.toggle('hidden');
  }
  
  document.addEventListener("click", function(event) {
    const filterBar = document.querySelector(".filter-bar");
    const filterToggleBtn = document.querySelector(".filter-btn");
  
    if (
      filterBar &&
      !filterBar.contains(event.target) &&
      (!filterToggleBtn || !filterToggleBtn.contains(event.target))
    ) {
      filterBar.classList.add("hidden");
    }
  });
  
  // ===== MONGODB INTEGRATION =====
  const API_BASE_URL = "http://localhost:3000/api"; // Thay đổi URL API
  
  // Hàm lấy tất cả sản phẩm
  async function loadAllProducts() {
    try {
      const response = await fetch(`${API_BASE_URL}/products`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const products = await response.json();
      displayProducts(products);
      return products;
    } catch (error) {
      console.error('Lỗi khi tải sản phẩm:', error);
      displayError('Không thể tải sản phẩm. Vui lòng thử lại sau.');
    }
  }
  
  // Hàm lọc sản phẩm
  async function filterProducts(filters) {
    try {
      const queryParams = new URLSearchParams();
      
      if (filters.category) {
        queryParams.append('category', filters.category);
      }
      if (filters.minPrice) {
        queryParams.append('minPrice', filters.minPrice);
      }
      if (filters.maxPrice) {
        queryParams.append('maxPrice', filters.maxPrice);
      }
      
      const response = await fetch(`${API_BASE_URL}/products/filter?${queryParams}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const products = await response.json();
      displayProducts(products);
      return products;
    } catch (error) {
      console.error('Lỗi khi lọc sản phẩm:', error);
      displayError('Không thể lọc sản phẩm. Vui lòng thử lại sau.');
    }
  }
  
  // Hàm sắp xếp sản phẩm
  async function sortProducts(sortType) {
    try {
      const response = await fetch(`${API_BASE_URL}/products/sort?sortBy=price&order=${sortType}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const products = await response.json();
      displayProducts(products);
      return products;
    } catch (error) {
      console.error('Lỗi khi sắp xếp sản phẩm:', error);
      displayError('Không thể sắp xếp sản phẩm. Vui lòng thử lại sau.');
    }
  }
  
  // Hàm hiển thị sản phẩm trên giao diện
  function displayProducts(products) {
    const productContainer = document.querySelector('.product-container') || 
                           document.querySelector('.products-grid') || 
                           document.querySelector('#products');
    
    if (!productContainer) {
      console.error('Không tìm thấy container để hiển thị sản phẩm');
      return;
    }
    
    if (products.length === 0) {
      productContainer.innerHTML = `
        <div class="no-products">
          <p>Không tìm thấy sản phẩm nào.</p>
        </div>
      `;
      return;
    }
    
    productContainer.innerHTML = products.map(product => `
      <div class="product-card" data-id="${product.id}">
        <div class="product-image">
          <img src="${product.img || './acsets/image/default-product.svg'}"
               alt="${product.title}"
               onerror="this.src='./acsets/image/default-product.svg'">
        </div>
        <div class="product-info">
          <h3 class="product-name">${product.title}</h3>
          <p class="product-price">${product.price.toLocaleString()} ₫</p>
          <p class="product-category">${product.category}</p>
          ${product.desc ? `<p class="product-description">${product.desc}</p>` : ''}
          <button class="add-to-cart-btn" onclick="addToCart('${product.id}', '${product.title}', ${product.price}, '${product.img}')">
            Thêm vào giỏ hàng
          </button>
        </div>
      </div>
    `).join('');
  }
  
  // Hàm hiển thị lỗi
  function displayError(message) {
    const productContainer = document.querySelector('.product-container') || 
                           document.querySelector('.products-grid') || 
                           document.querySelector('#products');
    
    if (productContainer) {
      productContainer.innerHTML = `
        <div class="error-message">
          <p>${message}</p>
          <button onclick="loadAllProducts()">Thử lại</button>
        </div>
      `;
    }
  }
  
  /*===== CART FUNCTIONALITY =====*/
  const CART_API_URL = `${API_BASE_URL}/cart`;

  function openCart() {
    document.querySelector(".cart-overlay").style.display = "block";
    document.querySelector(".cart-container").classList.add("active");
    loadCart();
  }

  function closeCart() {
    document.querySelector(".cart-overlay").style.display = "none";
    document.querySelector(".cart-container").classList.remove("active");
  }

  async function loadCart() {
    try {
      const res = await fetch(CART_API_URL);
      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status}`);
      }
      const items = await res.json();
      const cartContent = document.getElementById("cartContent");
      const cartTotal = document.getElementById("cartTotal");

      cartContent.innerHTML = "";
      let total = 0;

      if (items.length === 0) {
        cartContent.innerHTML = `
          <div class="cart-empty cart-section">
            <div class="cart-icon-wrapper">
              <div class="cart-icon">🛒<span class="cart-x">✖</span></div>
            </div>
            <p class="cart-empty-text">Không có sản phẩm nào trong giỏ hàng của bạn</p>
          </div>
        `;
        cartTotal.textContent = "0 ₫";
        return;
      }

      items.forEach(item => {
        total += item.price * item.quantity;

        const itemDiv = document.createElement("div");
        itemDiv.className = "cart-item cart-section";
        itemDiv.innerHTML = `
          <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #eee;">
            <div>
              <strong>${item.name}</strong><br/>
              <span>${item.price.toLocaleString()} ₫</span>
            </div>
            <div style="display: flex; align-items: center;">
              <button onclick="updateQuantity('${item._id}', ${item.quantity - 1})">-</button>
              <span style="margin: 0 10px;">${item.quantity}</span>
              <button onclick="updateQuantity('${item._id}', ${item.quantity + 1})">+</button>
              <button onclick="removeFromCart('${item._id}')" style="margin-left: 10px; color: red;">✕</button>
            </div>
          </div>
        `;
        cartContent.appendChild(itemDiv);
      });

      cartTotal.textContent = total.toLocaleString() + " ₫";
    } catch (error) {
      console.error('Lỗi khi tải giỏ hàng:', error);
      const cartContent = document.getElementById("cartContent");
      if (cartContent) {
        cartContent.innerHTML = `
          <div class="error-message">
            <p>Không thể tải giỏ hàng. Vui lòng thử lại sau.</p>
          </div>
        `;
      }
    }
  }

  // Hàm addToCart đã được chuyển sang cart.js và sử dụng global function

  async function updateQuantity(id, quantity) {
    if (quantity < 1) {
      await removeFromCart(id);
      return;
    }
    
    try {
      await fetch(`${CART_API_URL}/${id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ quantity })
      });
      loadCart();
    } catch (error) {
      console.error('Lỗi khi cập nhật số lượng:', error);
      showNotification('Không thể cập nhật số lượng!', 'error');
    }
  }

  async function removeFromCart(id) {
    try {
      await fetch(`${CART_API_URL}/${id}`, { method: "DELETE" });
      loadCart();
      showNotification('Đã xóa sản phẩm khỏi giỏ hàng!');
    } catch (error) {
      console.error('Lỗi khi xóa sản phẩm:', error);
      showNotification('Không thể xóa sản phẩm!', 'error');
    }
  }

  function checkoutCart() {
    alert("Chức năng thanh toán đang phát triển.");
  }

  // Hàm updateCartCount đã được chuyển sang cart.js
  
  // Hàm hiển thị thông báo
  function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 10px 20px;
      border-radius: 5px;
      color: white;
      z-index: 1000;
      background-color: ${type === 'error' ? '#f44336' : '#4CAF50'};
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.remove();
    }, 3000);
  }

  //NAV 
  let lastScrollTop = 0;
  let ticking = false;
  const nav = document.querySelector('.nav');

  window.addEventListener('scroll', () => {
    if (!ticking) {
      window.requestAnimationFrame(() => {
        const currentScroll = window.scrollY;
        const delta = currentScroll - lastScrollTop;

        // Cuộn xuống và đủ khoảng cách
        if (delta > 10 && currentScroll > 100) {
          nav.classList.add('shrink', 'hide');  // Ẩn và co lại
        } 
        // Cuộn lên và hiển thị lại ngay lập tức
        else if (delta < -10) {
          nav.classList.remove('hide');  // Loại bỏ class 'hide'
          nav.classList.add('shrink');   // Giữ hiệu ứng co lại nếu cần
        }

        // Nếu cuộn về đầu trang (currentScroll <= 50) thì hiển thị thanh nav 100%
        if (currentScroll <= 50) {
          nav.classList.remove('shrink', 'hide');
        }

        lastScrollTop = currentScroll <= 0 ? 0 : currentScroll;
        ticking = false;
      });

      ticking = true;
    }
  });
  
  // Khởi tạo trang - tải sản phẩm khi DOM đã sẵn sàng
  document.addEventListener('DOMContentLoaded', () => {
    loadAllProducts();
  });