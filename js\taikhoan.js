// Authentication state variables
let currentAccount = JSON.parse(localStorage.getItem('currentAccount')) || null;

// DOM elements
const loginModal = document.getElementById('loginModal');
const registerModal = document.getElementById('registerModal');
const loginPhoneInput = document.querySelector('#loginModal .modal-input[placeholder="Nhập số điện thoại"]');
const loginPasswordInput = document.querySelector('#loginModal .modal-input[placeholder="Nhập mật khẩu"]');
const loginButton = document.querySelector('#loginModal .btn-login');

const registerNameInput = document.getElementById('nameInput');
const registerPhoneInput = document.getElementById('phoneInput');
const registerPasswordInput = document.getElementById('passwordInput');
const registerConfirmPasswordInput = document.getElementById('confirmPasswordInput');
const registerPolicyCheckbox = document.getElementById('policyCheckbox');
const registerButton = document.getElementById('registerBtn');

// Account menu elements (to be added to HTML)
const accountMenuHTML = `
<div class="account-menu" id="accountMenu" style="display: none;">
    <div class="account-info">
        <p id="userFullName">Khách hàng</p>
        <p id="userRole"></p>
    </div>
    <ul>
        <li id="profileLink"><a href="#">Thông tin tài khoản</a></li>
        <li id="ordersLink"><a href="#">Đơn hàng của tôi</a></li>
        <li id="adminLink" style="display: none;"><a href="admin.html">Quản lý</a></li>
        <li id="staffLink" style="display: none;"><a href="staff.html">Nhân viên</a></li>
        <li id="logoutBtn"><a href="#">Đăng xuất</a></li>
    </ul>
</div>
`;

// Add account menu to the DOM after page loads
document.addEventListener('DOMContentLoaded', () => {
    // Create account menu element
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = accountMenuHTML;
    document.body.appendChild(tempDiv.firstElementChild);
    
    // Update header if user is logged in
    updateAuthUI();
    
    // Update new UI
    updateUserInterface();
    
    // Setup event listeners
    setupEventListeners();
});

// Setup all authentication related event listeners
function setupEventListeners() {
    // Login button
    if (loginButton) {
        loginButton.addEventListener('click', handleLogin);
    }
    
    // Register button
    if (registerButton) {
        registerButton.addEventListener('click', handleRegister);
    }
    
    // User menu toggle
    document.addEventListener('click', function(e) {
        const userAvatar = document.getElementById('userAvatar');
        const accountMenu = document.getElementById('accountMenu');
        
        if (userAvatar && e.target === userAvatar || e.target.closest('#userAvatar')) {
            if (accountMenu) {
                accountMenu.style.display = accountMenu.style.display === 'none' ? 'block' : 'none';
            }
        } else if (accountMenu && !e.target.closest('#accountMenu')) {
            accountMenu.style.display = 'none';
        }
    });
    
    // Account dropdown toggle
    const accountDropdownToggle = document.querySelector('.text-tk');
    if (accountDropdownToggle) {
        accountDropdownToggle.addEventListener('click', function() {
            const dropdown = document.querySelector('.account-dropdown-list');
            if (dropdown) {
                dropdown.classList.toggle('show');
            }
        });
        
        // Hide dropdown when clicking elsewhere
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.text-tk') && !e.target.closest('.account-dropdown-list')) {
                const dropdown = document.querySelector('.account-dropdown-list');
                if (dropdown && dropdown.classList.contains('show')) {
                    dropdown.classList.remove('show');
                }
            }
        });
    }
    
    // Logout
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', handleLogout);
    }
}

// Handle user login
function handleLogin() {
    const phone = loginPhoneInput.value.trim();
    const password = loginPasswordInput.value.trim();
    
    if (!phone || !password) {
        alert('Vui lòng nhập đầy đủ thông tin!');
        return;
    }
    
    fetch('http://localhost:3000/account')
        .then(res => res.json())
        .then(accounts => {
            const account = accounts.find(acc => 
                (acc.phone === phone || acc.email === phone) && acc.password === password
            );
            
            if (account) {
                // Add ID property if not present (assuming array index + 1 as ID)
                if (!account.id) {
                    account.id = accounts.findIndex(acc => 
                        (acc.phone === phone || acc.email === phone) && acc.password === password
                    ) + 1;
                }
                
                // Save to local storage
                localStorage.setItem('currentAccount', JSON.stringify(account));
                currentAccount = account;
                
                // Update UI
                updateAuthUI();
                updateUserInterface();
                
                // Close modal
                closeModal('loginModal');
                
                // Show welcome message
                alert(`Xin chào ${account.fullname}!`);
                
                // Redirect admin/staff if needed
                if (account.role === 'admin') {
                    // Optional: redirect or show admin controls
                    document.getElementById('adminLink').style.display = 'block';
                } else if (account.role === 'staff') {
                    // Optional: redirect or show staff controls
                    document.getElementById('staffLink').style.display = 'block';
                }
                
                // Update cart if needed
                updateCartCount();
            } else {
                alert('Số điện thoại/email hoặc mật khẩu không đúng!');
            }
        })
        .catch(error => {
            console.error('Lỗi khi đăng nhập:', error);
            alert('Đã xảy ra lỗi khi đăng nhập. Vui lòng thử lại sau!');
        });
}

// Handle user registration
function handleRegister() {
    const fullname = registerNameInput.value.trim();
    const phone = registerPhoneInput.value.trim();
    const password = registerPasswordInput.value.trim();
    const confirmPassword = registerConfirmPasswordInput.value.trim();
    const policyAgreed = registerPolicyCheckbox.checked;
    
    // Validation
    if (!fullname || !phone || !password || !confirmPassword) {
        alert('Vui lòng nhập đầy đủ thông tin!');
        return;
    }
    
    if (password !== confirmPassword) {
        alert('Mật khẩu nhập lại không khớp!');
        return;
    }
    
    if (!policyAgreed) {
        alert('Vui lòng đồng ý với chính sách của chúng tôi!');
        return;
    }
    
    // Check if phone number already exists
    fetch('http://localhost:3000/account')
        .then(res => res.json())
        .then(accounts => {
            const exists = accounts.some(acc => acc.phone === phone);
            
            if (exists) {
                alert('Số điện thoại đã được sử dụng!');
                return;
            }
            
            // Create new account
            const newAccount = {
                fullname: fullname,
                phone: phone,
                password: password,
                email: '',
                status: 1,
                join: new Date().toISOString(),
                cart: [],
                userType: 1,
                role: 'user'  // Default role
            };
            
            // Add to database
            fetch('http://localhost:3000/account', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(newAccount)
            })
            .then(res => {
                if (!res.ok) {
                    throw new Error('Đăng ký thất bại');
                }
                return res.json();
            })
            .then(data => {
                alert('Đăng ký thành công!');
                
                // Auto login the new user
                localStorage.setItem('currentAccount', JSON.stringify(data));
                currentAccount = data;
                
                // Update UI
                updateAuthUI();
                updateUserInterface();
                
                // Close modal
                closeModal('registerModal');
            })
            .catch(error => {
                console.error('Lỗi khi đăng ký:', error);
                alert('Đăng ký thất bại! Vui lòng thử lại sau.');
            });
        })
        .catch(error => {
            console.error('Lỗi khi kiểm tra tài khoản:', error);
            alert('Đã xảy ra lỗi khi đăng ký. Vui lòng thử lại sau!');
        });
}

// Handle user logout
function handleLogout(e) {
    e.preventDefault();
    
    // Clear local storage
    localStorage.removeItem('currentAccount');
    currentAccount = null;
    
    // Update UI
    updateAuthUI();
    updateUserInterface();
    
    // Reset cart display
    updateCartCount();
    
    // Close account menu
    document.getElementById('accountMenu').style.display = 'none';
    
    // Redirect to home if on admin/staff page
    if (window.location.href.includes('admin.html') || window.location.href.includes('staff.html')) {
        window.location.href = 'index.html';
    }
}

// Update UI based on authentication status
function updateAuthUI() {
    // Get header elements
    const headerRight = document.querySelector('.header-right');
    
    if (!headerRight) return;
    
    // Check if user avatar exists, create if not
    let userAvatar = document.getElementById('userAvatar');
    
    if (!userAvatar) {
        // Create login/register links if user avatar doesn't exist
        const authLinks = document.createElement('div');
        authLinks.className = 'auth-links';
        authLinks.innerHTML = `
            <a href="#" onclick="openModal('loginModal')">Đăng nhập</a> / 
            <a href="#" onclick="openModal('registerModal')">Đăng ký</a>
        `;
        
        // Create user avatar element
        userAvatar = document.createElement('div');
        userAvatar.id = 'userAvatar';
        userAvatar.className = 'user-avatar';
        userAvatar.style.display = 'none';
        userAvatar.innerHTML = '<img src="img/user-avatar.png" alt="User">';
        
        // Add to header
        headerRight.appendChild(authLinks);
        headerRight.appendChild(userAvatar);
    }
    
    // Get auth links
    const authLinks = document.querySelector('.auth-links');
    
    // Update UI based on login status
    if (currentAccount) {
        // Show avatar, hide login links
        if (userAvatar) userAvatar.style.display = 'block';
        if (authLinks) authLinks.style.display = 'none';
        
        // Update user info in account menu
        const userFullName = document.getElementById('userFullName');
        const userRole = document.getElementById('userRole');
        const adminLink = document.getElementById('adminLink');
        const staffLink = document.getElementById('staffLink');
        
        if (userFullName) userFullName.textContent = currentAccount.fullname || 'Khách hàng';
        if (userRole) {
            switch (currentAccount.role) {
                case 'admin':
                    userRole.textContent = 'Quản trị viên';
                    break;
                case 'staff':
                    userRole.textContent = 'Nhân viên';
                    break;
                default:
                    userRole.textContent = 'Khách hàng';
            }
        }
        
        // Show/hide admin links based on role
        if (adminLink) adminLink.style.display = currentAccount.role === 'admin' ? 'block' : 'none';
        if (staffLink) staffLink.style.display = currentAccount.role === 'staff' || currentAccount.role === 'admin' ? 'block' : 'none';
    } else {
        // Hide avatar, show login links
        if (userAvatar) userAvatar.style.display = 'none';
        if (authLinks) authLinks.style.display = 'block';
    }
}

// Update user interface based on login status
function updateUserInterface() {
    const authContainer = document.querySelector('.auth-container');
    const accountDropdown = document.querySelector('.account-dropdown-list');
    
    if (!authContainer || !accountDropdown) return;
    
    if (currentAccount) {
        // User is logged in
        authContainer.innerHTML = `
            <span class="text-username">Xin chào, ${currentAccount.fullname}</span>
            <span class="text-tk">Tài khoản <i class="fa-solid fa-caret-down"></i></span>
        `;
        
        accountDropdown.innerHTML = `
            <li class="account-dropdown-item" onclick="viewAccount()">
                <i class="fa-solid fa-user-circle"></i> Thông tin tài khoản
            </li>
            <li class="account-dropdown-item" onclick="viewOrders()">
                <i class="fa-solid fa-clipboard-list"></i> Đơn hàng của tôi
            </li>
            ${currentAccount.role === 'admin' ? 
            `<li class="account-dropdown-item" onclick="window.location.href='admin.html'">
                <i class="fa-solid fa-cog"></i> Quản lý
            </li>` : ''}
            ${(currentAccount.role === 'staff' || currentAccount.role === 'admin') ? 
            `<li class="account-dropdown-item" onclick="window.location.href='staff.html'">
                <i class="fa-solid fa-briefcase"></i> Nhân viên
            </li>` : ''}
            <li class="account-dropdown-item" onclick="logout()">
                <i class="fa-solid fa-sign-out-alt"></i> Đăng xuất
            </li>
        `;
    } else {
        // User is not logged in
        authContainer.innerHTML = `
            <span class="text-dndk">Đăng nhập / Đăng ký</span>
            <span class="text-tk">Tài khoản <i class="fa-solid fa-caret-down"></i></span>
        `;
        
        accountDropdown.innerHTML = `
            <li class="account-dropdown-item" onclick="openLoginModal()">
                <i class="fa-solid fa-right-to-bracket"></i> Đăng nhập
            </li>
            <li class="account-dropdown-item" onclick="openRegisterModal()">
                <i class="fa-solid fa-user-plus"></i> Đăng ký
            </li>
        `;
    }
}

// Open modal dialog
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) modal.style.display = 'block';
}

// Close modal dialog
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) modal.style.display = 'none';
}

// Switch to login modal
function switchToLogin() {
    closeModal('registerModal');
    openModal('loginModal');
}

// Switch to register modal
function switchToRegister() {
    closeModal('loginModal');
    openModal('registerModal');
}

// Protect admin pages
function checkAdminAccess() {
    // Only run on admin pages
    if (window.location.href.includes('admin.html')) {
        if (!currentAccount || currentAccount.role !== 'admin') {
            alert('Bạn không có quyền truy cập trang này!');
            window.location.href = 'index.html';
        }
    }
}

// Protect staff pages
function checkStaffAccess() {
    // Only run on staff pages
    if (window.location.href.includes('staff.html')) {
        if (!currentAccount || (currentAccount.role !== 'staff' && currentAccount.role !== 'admin')) {
            alert('Bạn không có quyền truy cập trang này!');
            window.location.href = 'index.html';
        }
    }
}

// Run access checks on page load
document.addEventListener('DOMContentLoaded', () => {
    checkAdminAccess();
    checkStaffAccess();
});

// Update cart - modified to work with the authentication system
function updateCartCount() {
    if (!currentAccount) return;
    
    // Get cart data
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    const userCart = cart.filter(item => item.userId === currentAccount.id);
    const cartCount = userCart.reduce((sum, item) => sum + item.quantity, 0);
    
    // Update cart count display
    const cartCountElement = document.getElementById('cart-count');
    if (cartCountElement) {
        cartCountElement.textContent = cartCount;
        cartCountElement.style.display = cartCount > 0 ? 'block' : 'none';
    }
}

// View account information
function viewAccount() {
    if (!currentAccount) {
        openLoginModal();
        return;
    }
    
    // Redirect to account page or open account modal
    // This implementation depends on your application structure
    // Example:
    // window.location.href = 'account.html';
    alert('Tính năng đang được phát triển');
}

// View orders
function viewOrders() {
    if (!currentAccount) {
        openLoginModal();
        return;
    }
    
    // Redirect to orders page or open orders modal
    // This implementation depends on your application structure
    // Example:
    // window.location.href = 'orders.html';
    alert('Tính năng đang được phát triển');
}

// Logout function - for new UI
function logout() {
    handleLogout(new Event('click'));
}

// Open login modal - for new UI
function openLoginModal() {
    openModal('loginModal');
    
    // Hide any open dropdowns
    const dropdown = document.querySelector('.account-dropdown-list');
    if (dropdown && dropdown.classList.contains('show')) {
        dropdown.classList.remove('show');
    }
    
    const accountMenu = document.getElementById('accountMenu');
    if (accountMenu) accountMenu.style.display = 'none';
}

// Open register modal - for new UI
function openRegisterModal() {
    openModal('registerModal');
    
    // Hide any open dropdowns
    const dropdown = document.querySelector('.account-dropdown-list');
    if (dropdown && dropdown.classList.contains('show')) {
        dropdown.classList.remove('show');
    }
    
    const accountMenu = document.getElementById('accountMenu');
    if (accountMenu) accountMenu.style.display = 'none';
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', () => {
    updateAuthUI();
    updateUserInterface();
});