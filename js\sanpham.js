
let currentProduct = null;
let currentQty = 1;
let allProducts = [];
let currentPage = 1;
const productsPerPage = 12;
// Khởi tạo giỏ hàng từ localStorage hoặc tạo mới nếu chưa có
let cart = JSON.parse(localStorage.getItem('cart')) || [];

// Fetch dữ liệu sản phẩm
fetch('http://localhost:3000/products')
  .then(res => res.json())
  .then(products => {
    allProducts = products;
    renderProducts(currentPage);
    updateCartCount(); // Cập nhật số lượng giỏ hàng khi tải trang
  })
  .catch(error => {
    console.error('Lỗi khi tải sản phẩm:', error);
    document.getElementById('home-products').innerHTML = '<p>Không thể tải sản phẩm. Vui lòng thử lại sau.</p>';
  });

// Hiển thị sản phẩm theo trang
function renderProducts(page) {
  const start = (page - 1) * productsPerPage;
  const end = start + productsPerPage;
  const currentProducts = allProducts.slice(start, end);
  const container = document.getElementById('home-products');
  container.innerHTML = '';

  currentProducts.forEach(product => {
    const div = document.createElement('div');
    div.classList.add('product-card');
    div.innerHTML = `
      <img src="${product.img}" alt="${product.title}" />
      <h3>${product.title}</h3>
      <p><strong>${product.price.toLocaleString()}₫</strong></p>
      <button class="btn-order">ĐẶT MÓN</button>
    `;
    div.querySelector('.btn-order').addEventListener('click', () => openProductModal(product));
    container.appendChild(div);
  });

  renderPagination(allProducts.length, page);
}

// Hiển thị thanh phân trang
function renderPagination(totalItems, currentPage) {
  const totalPages = Math.ceil(totalItems / productsPerPage);
  const pageNav = document.getElementById("page-nav-list");
  pageNav.innerHTML = '';

  for (let i = 1; i <= totalPages; i++) {
    const li = document.createElement('li');
    li.textContent = i;
    li.classList.toggle('active', i === currentPage);
    li.onclick = () => {
      currentPage = i;
      renderProducts(currentPage);
    };
    pageNav.appendChild(li);
  }
}

// ------------------ Modal & Đặt hàng ------------------ //
const modal = document.getElementById("productModal");
const modalImage = document.getElementById("modalImage");
const modalName = document.getElementById("modalName");
const modalPrice = document.getElementById("modalPrice");
const modalDesc = document.getElementById("modalDesc");
const modalTotal = document.getElementById("modalTotal");
const quantitySpan = document.getElementById("quantity");
const note = document.getElementById("note");

// Đóng modal khi click vào nút đóng
document.querySelector(".close").onclick = () => {
  modal.style.display = "none";
  // Reset các giá trị
  currentQty = 1;
  note.value = '';
};

function openProductModal(product) {
  currentProduct = product;
  currentQty = 1;

  modalImage.src = product.img;
  modalName.textContent = product.title;
  modalPrice.textContent = product.price.toLocaleString() + "₫";
  modalDesc.textContent = product.desc || "Không có mô tả cho sản phẩm này.";
  quantitySpan.textContent = currentQty;
  modalTotal.textContent = product.price.toLocaleString() + "₫";
  note.value = '';

  modal.style.display = 'block'; // Giữ nguyên kiểu hiển thị như trong HTML gốc
}

document.getElementById("increase").onclick = () => {
  currentQty++;
  quantitySpan.textContent = currentQty;
  updateTotal();
};

document.getElementById("decrease").onclick = () => {
  if (currentQty > 1) currentQty--;
  quantitySpan.textContent = currentQty;
  updateTotal();
};

function updateTotal() {
  const total = currentQty * currentProduct.price;
  modalTotal.textContent = total.toLocaleString() + "₫";
}

// Thêm vào giỏ hàng - Kết nối với nút cartIcon hiện có
document.getElementById("cartIcon").onclick = () => {
  addToCart();
};

function addToCart() {
  if (!currentProduct) return;

  // Sử dụng hệ thống giỏ hàng mới
  if (window.cartManager) {
    window.cartManager.addToCart({
      id: currentProduct.id,
      title: currentProduct.title,
      price: currentProduct.price,
      img: currentProduct.img,
      quantity: currentQty,
      note: note.value
    });

    modal.style.display = 'none';
  } else {
    alert('Hệ thống giỏ hàng chưa sẵn sàng!');
  }
}

// Hàm updateCartCount đã được chuyển sang cart.js

// Gửi đơn hàng
document.getElementById("orderNow").onclick = () => {
  const account = JSON.parse(localStorage.getItem('currentAccount'));
  if (!account) {
    alert('Vui lòng đăng nhập trước khi đặt hàng!');
    return;
  }

  const order = {
    userId: account.id,
    userName: account.username || account.email,
    productId: currentProduct.id,
    productName: currentProduct.title,
    productImg: currentProduct.img,
    price: currentProduct.price,
    quantity: currentQty,
    note: note.value,
    total: currentProduct.price * currentQty,
    status: "pending",
    date: new Date().toISOString()
  };

  fetch('http://localhost:3000/orders', {  // Sửa endpoint thành /orders
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(order)
  })
  .then(res => {
    if (!res.ok) {
      throw new Error('Đặt hàng thất bại');
    }
    return res.json();
  })
  .then(data => {
    alert('Đặt hàng thành công!');
    modal.style.display = 'none';
  })
  .catch(error => {
    console.error('Lỗi khi đặt hàng:', error);
    alert('Đặt hàng thất bại! Vui lòng thử lại sau.');
  });
};