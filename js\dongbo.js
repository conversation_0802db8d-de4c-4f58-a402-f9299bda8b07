/**
 * Module đồng bộ dữ liệu giữa trang người dùng và admin
 * - Lưu trữ dữ liệu vào local storage và db.json thông qua API
 * - Đồng bộ hóa dữ liệu giữa các tab
 * - Xử lý xung đột khi cả hai tab cùng cập nhật dữ liệu
 */

// API Endpoints cho JSON Server
const API_BASE_URL = "http://localhost:3000"; // JSON Server chạy ở cổng 3000
const PRODUCTS_API = API_BASE_URL + "/products";
const ORDERS_API = API_BASE_URL + "/orders";
const USERS_API = API_BASE_URL + "/account"; // Endpoint cho tài khoản người dùng

// Xác định vai trò hiện tại (admin hoặc user)
const isAdmin = window.location.href.includes('admin.html');

// Các kho dữ liệu và timestamp
let dataStore = {
  products: [],
  orders: [],
  users: [],
  lastSync: {
    products: 0,
    orders: 0,
    users: 0
  }
};

// Khởi tạo hệ thống đồng bộ khi trang được tải
document.addEventListener('DOMContentLoaded', function() {
  console.log('Đang khởi tạo hệ thống đồng bộ dữ liệu...');
  
  // Tải dữ liệu từ localStorage trước
  loadLocalData();
  
  // Tải dữ liệu mẫu từ API JSON nếu chưa có dữ liệu
  initFromJsonApi();
  
  // Đồng bộ với API JSON
  syncWithAPI();
  
  // Thiết lập sự kiện lắng nghe localStorage để đồng bộ giữa các tab
  setupStorageListener();
  
  // Thiết lập đồng bộ định kỳ
  setupPeriodicSync();
});

/**
 * Tải dữ liệu từ localStorage
 */
function loadLocalData() {
  // Tải các thông tin lưu trữ
  dataStore.products = JSON.parse(localStorage.getItem('products')) || [];
  dataStore.orders = JSON.parse(localStorage.getItem('orders')) || [];
  dataStore.users = JSON.parse(localStorage.getItem('users')) || [];
  
  // Tải timestamp
  const lastSync = JSON.parse(localStorage.getItem('lastSync')) || {
    products: 0,
    orders: 0,
    users: 0
  };
  dataStore.lastSync = lastSync;
  
  console.log('Đã tải dữ liệu từ localStorage:', 
    `Sản phẩm: ${dataStore.products.length}, ` +
    `Đơn hàng: ${dataStore.orders.length}, ` +
    `Người dùng: ${dataStore.users.length}`);
}

/**
 * Đồng bộ hóa dữ liệu với API JSON
 * @export Hàm này được export cho các file khác sử dụng
 */
function syncWithAPI() {
  // Export hàm cho toàn cục
  window.syncWithAPI = syncWithAPI;
  console.log('Đang đồng bộ với API JSON...');
  
  // Kiểm tra kết nối API
  fetch(API_BASE_URL)
    .then(response => {
      if (!response.ok) {
        throw new Error('Không thể kết nối đến API');
      }
      
      // Nếu có kết nối, tiến hành đồng bộ
      return Promise.all([
        syncProducts(),
        syncOrders(),
        syncUsers()
      ]);
    })
    .then(() => {
      console.log('Đồng bộ dữ liệu với API JSON thành công');
      
      // Cập nhật thời gian đồng bộ
      const now = Date.now();
      dataStore.lastSync = {
        products: now,
        orders: now,
        users: now
      };
      localStorage.setItem('lastSync', JSON.stringify(dataStore.lastSync));
      
      // Thông báo sự kiện đồng bộ
      dispatchSyncEvent('all');
    })
    .catch(error => {
      console.error('Lỗi khi đồng bộ với API JSON:', error);
      
      // Nếu không kết nối được với API, sử dụng dữ liệu từ localStorage
      console.log('Sử dụng dữ liệu từ localStorage thay thế');
    });
}

/**
 * Đồng bộ dữ liệu sản phẩm với API JSON
 */
function syncProducts() {
  return new Promise((resolve, reject) => {
    // Tải sản phẩm từ API
    fetch(PRODUCTS_API)
      .then(response => {
        if (!response.ok) {
          throw new Error('Không thể tải dữ liệu sản phẩm từ API');
        }
        return response.json();
      })
      .then(apiProducts => {
        // Cập nhật dữ liệu sản phẩm từ API
        dataStore.products = mergeProductData(dataStore.products, apiProducts);
        
        // Lưu vào localStorage để sử dụng khi không có kết nối
        localStorage.setItem('products', JSON.stringify(dataStore.products));
        
        // Đồng bộ ngược lại lên API nếu có sản phẩm mới và người dùng là admin
        const unsyncedProducts = dataStore.products.filter(p => !p.synced);
        if (unsyncedProducts.length > 0 && isAdmin) {
          console.log(`Đang đồng bộ ${unsyncedProducts.length} sản phẩm lên API...`);
          
          // Đồng bộ từng sản phẩm
          const syncPromises = unsyncedProducts.map(product => {
            const method = product.isNew ? 'POST' : 'PUT';
            const url = product.isNew ? PRODUCTS_API : `${PRODUCTS_API}/${product.id}`;
            
            return fetch(url, {
              method: method,
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(product)
            })
            .then(response => {
              if (!response.ok) {
                throw new Error(`Không thể đồng bộ sản phẩm ${product.id}`);
              }
              return response.json();
            })
            .then(data => {
              // Đánh dấu đã đồng bộ
              const index = dataStore.products.findIndex(p => p.id === product.id);
              if (index !== -1) {
                dataStore.products[index].synced = true;
                dataStore.products[index].isNew = false;
              }
            });
          });
          
          // Chờ tất cả đồng bộ hoàn tất
          Promise.all(syncPromises)
            .then(() => {
              // Cập nhật lại localStorage
              localStorage.setItem('products', JSON.stringify(dataStore.products));
              resolve();
            })
            .catch(error => {
              console.error('Lỗi khi đồng bộ sản phẩm lên API:', error);
              resolve(); // Vẫn tiếp tục quy trình
            });
        } else {
          resolve();
        }
      })
      .catch(error => {
        console.error('Lỗi khi đồng bộ sản phẩm:', error);
        
        // Sử dụng dữ liệu từ localStorage khi không thể kết nối API
        console.log('Sử dụng dữ liệu sản phẩm từ localStorage...');
        const cachedProducts = localStorage.getItem('products');
        if (cachedProducts) {
          dataStore.products = JSON.parse(cachedProducts);
        }
        
        resolve();
      });
  });
}

/**
 * Đồng bộ dữ liệu đơn hàng với API JSON
 */
function syncOrders() {
  return new Promise((resolve, reject) => {
    // Tải đơn hàng từ API
    fetch(ORDERS_API)
      .then(response => {
        if (!response.ok) {
          throw new Error('Không thể tải dữ liệu đơn hàng từ API');
        }
        return response.json();
      })
      .then(apiOrders => {
        // Cập nhật dữ liệu đơn hàng từ API
        dataStore.orders = mergeOrderData(dataStore.orders, apiOrders);
        
        // Lưu vào localStorage để sử dụng khi không có kết nối
        localStorage.setItem('orders', JSON.stringify(dataStore.orders));
        
        // Cập nhật lịch sử đơn hàng của người dùng
        const currentAccount = JSON.parse(localStorage.getItem('currentAccount'));
        if (currentAccount) {
          const userOrders = dataStore.orders.filter(order => order.userId === currentAccount.id);
          localStorage.setItem('orderHistory', JSON.stringify(userOrders));
        }
        
        // Đồng bộ ngược lại lên API nếu có đơn hàng mới
        const unsyncedOrders = dataStore.orders.filter(order => !order.synced);
        if (unsyncedOrders.length > 0) {
          console.log(`Đang đồng bộ ${unsyncedOrders.length} đơn hàng lên API...`);
          
          // Đồng bộ từng đơn hàng
          const syncPromises = unsyncedOrders.map(order => {
            // Đơn hàng mới thì POST, đơn hàng cập nhật thì PATCH
            const isNewOrder = !apiOrders.some(o => o.id === order.id);
            const method = isNewOrder ? 'POST' : 'PATCH';
            const url = isNewOrder ? ORDERS_API : `${ORDERS_API}/${order.id}`;
            
            return fetch(url, {
              method: method,
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(order)
            })
            .then(response => {
              if (!response.ok) {
                throw new Error(`Không thể đồng bộ đơn hàng ${order.id}`);
              }
              return response.json();
            })
            .then(data => {
              // Đánh dấu đã đồng bộ
              const index = dataStore.orders.findIndex(o => o.id === order.id);
              if (index !== -1) {
                dataStore.orders[index].synced = true;
              }
            });
          });
          
          // Chờ tất cả đồng bộ hoàn tất
          Promise.all(syncPromises)
            .then(() => {
              // Cập nhật lại localStorage
              localStorage.setItem('orders', JSON.stringify(dataStore.orders));
              
              // Cập nhật lại lịch sử đơn hàng
              if (currentAccount) {
                const userOrders = dataStore.orders.filter(order => order.userId === currentAccount.id);
                localStorage.setItem('orderHistory', JSON.stringify(userOrders));
              }
              
              resolve();
            })
            .catch(error => {
              console.error('Lỗi khi đồng bộ đơn hàng lên API:', error);
              resolve(); // Vẫn tiếp tục quy trình
            });
        } else {
          resolve();
        }
      })
      .catch(error => {
        console.error('Lỗi khi đồng bộ đơn hàng:', error);
        
        // Sử dụng dữ liệu từ localStorage khi không thể kết nối API
        console.log('Sử dụng dữ liệu đơn hàng từ localStorage...');
        const cachedOrders = localStorage.getItem('orders');
        if (cachedOrders) {
          dataStore.orders = JSON.parse(cachedOrders);
        }
        
        // Cập nhật lịch sử đơn hàng của người dùng
        const currentAccount = JSON.parse(localStorage.getItem('currentAccount'));
        if (currentAccount) {
          const userOrders = dataStore.orders.filter(order => order.userId === currentAccount.id);
          localStorage.setItem('orderHistory', JSON.stringify(userOrders));
        }
        
        resolve();
      });
  });
}

/**
 * Đồng bộ dữ liệu người dùng với API JSON
 */
function syncUsers() {
  // Chỉ admin mới có quyền đồng bộ đầy đủ dữ liệu người dùng
  if (!isAdmin) {
    // Người dùng thường chỉ đồng bộ thông tin cá nhân
    return syncCurrentUser();
  }
  
  return new Promise((resolve, reject) => {
    // Tải người dùng từ API
    fetch(USERS_API)
      .then(response => {
        if (!response.ok) {
          throw new Error('Không thể tải dữ liệu người dùng từ API');
        }
        return response.json();
      })
      .then(apiUsers => {
        // Cập nhật dữ liệu người dùng từ API
        dataStore.users = mergeUserData(dataStore.users, apiUsers);
        
        // Lưu vào localStorage để sử dụng khi không có kết nối
        localStorage.setItem('users', JSON.stringify(dataStore.users));
        
        // Đồng bộ ngược lại lên API nếu có người dùng mới
        const unsyncedUsers = dataStore.users.filter(user => !user.synced);
        if (unsyncedUsers.length > 0) {
          console.log(`Đang đồng bộ ${unsyncedUsers.length} người dùng lên API...`);
          
          // Đồng bộ từng người dùng
          const syncPromises = unsyncedUsers.map(user => {
            // Người dùng mới thì POST, người dùng cập nhật thì PATCH
            const isNewUser = !apiUsers.some(u => u.id === user.id);
            const method = isNewUser ? 'POST' : 'PATCH';
            const url = isNewUser ? USERS_API : `${USERS_API}/${user.id}`;
            
            return fetch(url, {
              method: method,
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(user)
            })
            .then(response => {
              if (!response.ok) {
                throw new Error(`Không thể đồng bộ người dùng ${user.id}`);
              }
              return response.json();
            })
            .then(data => {
              // Đánh dấu đã đồng bộ
              const index = dataStore.users.findIndex(u => u.id === user.id);
              if (index !== -1) {
                dataStore.users[index].synced = true;
                dataStore.users[index].isNew = false;
              }
            });
          });
          
          // Chờ tất cả đồng bộ hoàn tất
          Promise.all(syncPromises)
            .then(() => {
              // Cập nhật lại localStorage
              localStorage.setItem('users', JSON.stringify(dataStore.users));
              resolve();
            })
            .catch(error => {
              console.error('Lỗi khi đồng bộ người dùng lên API:', error);
              resolve(); // Vẫn tiếp tục quy trình
            });
        } else {
          resolve();
        }
      })
      .catch(error => {
        console.error('Lỗi khi đồng bộ người dùng:', error);
        
        // Sử dụng dữ liệu từ localStorage khi không thể kết nối API
        console.log('Sử dụng dữ liệu người dùng từ localStorage...');
        const cachedUsers = localStorage.getItem('users');
        if (cachedUsers) {
          dataStore.users = JSON.parse(cachedUsers);
        }
        
        resolve();
      });
  });
}

/**
 * Đồng bộ thông tin người dùng hiện tại
 */
function syncCurrentUser() {
  const currentAccount = JSON.parse(localStorage.getItem('currentAccount'));
  if (!currentAccount) return Promise.resolve(false);
  
  return new Promise((resolve, reject) => {
    // Chỉ lấy thông tin của người dùng hiện tại từ API
    fetch(`${USERS_API}/${currentAccount.id}`)
      .then(response => {
        if (!response.ok) {
          throw new Error(`Không thể tải thông tin người dùng ${currentAccount.id}`);
        }
        return response.json();
      })
      .then(apiUser => {
        // Cập nhật currentAccount nếu API có phiên bản mới hơn
        if (apiUser.updatedAt && (!currentAccount.updatedAt || 
            new Date(apiUser.updatedAt) > new Date(currentAccount.updatedAt))) {
          
          localStorage.setItem('currentAccount', JSON.stringify(apiUser));
        }
        
        // Cập nhật thông tin người dùng vào dataStore
        const userIndex = dataStore.users.findIndex(u => u.id === currentAccount.id);
        if (userIndex !== -1) {
          dataStore.users[userIndex] = apiUser;
        } else {
          dataStore.users.push(apiUser);
        }
        
        localStorage.setItem('users', JSON.stringify(dataStore.users));
        resolve(true);
      })
      .catch(error => {
        console.error('Lỗi khi đồng bộ thông tin người dùng hiện tại:', error);
        resolve(false);
      });
  });
}

/**
 * Gộp dữ liệu sản phẩm từ local và API
 */
function mergeProductData(localProducts, apiProducts) {
  // Clone để tránh ảnh hưởng tới dữ liệu gốc
  const merged = [...localProducts];
  
  // Thêm các sản phẩm từ API nếu chưa có
  apiProducts.forEach(apiProduct => {
    const existingIndex = merged.findIndex(p => p.id === apiProduct.id);
    
    if (existingIndex === -1) {
      // Thêm mới nếu chưa có
      apiProduct.synced = true;
      merged.push(apiProduct);
    } else {
      // Cập nhật nếu API có phiên bản mới hơn
      const localProduct = merged[existingIndex];
      if (!localProduct.updatedAt || 
          (apiProduct.updatedAt && new Date(apiProduct.updatedAt) > new Date(localProduct.updatedAt))) {
        
        // Giữ lại trạng thái đồng bộ
        apiProduct.synced = true;
        merged[existingIndex] = apiProduct;
      }
    }
  });
  
  return merged;
}

/**
 * Gộp dữ liệu đơn hàng từ local và API
 */
function mergeOrderData(localOrders, apiOrders) {
  // Clone để tránh ảnh hưởng tới dữ liệu gốc
  const merged = [...localOrders];
  
  // Thêm các đơn hàng từ API nếu chưa có
  apiOrders.forEach(apiOrder => {
    const existingIndex = merged.findIndex(o => o.id === apiOrder.id);
    
    if (existingIndex === -1) {
      // Thêm mới nếu chưa có
      apiOrder.synced = true;
      merged.push(apiOrder);
    } else {
      // Cập nhật nếu API có phiên bản mới hơn
      const localOrder = merged[existingIndex];
      if (!localOrder.updatedAt || 
          (apiOrder.updatedAt && new Date(apiOrder.updatedAt) > new Date(localOrder.updatedAt))) {
        
        // Giữ lại trạng thái đồng bộ
        apiOrder.synced = true;
        merged[existingIndex] = apiOrder;
      }
    }
  });
  
  return merged;
}

/**
 * Gộp dữ liệu người dùng từ local và API
 */
function mergeUserData(localUsers, apiUsers) {
  // Clone để tránh ảnh hưởng tới dữ liệu gốc
  const merged = [...localUsers];
  
  // Thêm các người dùng từ API nếu chưa có
  apiUsers.forEach(apiUser => {
    const existingIndex = merged.findIndex(u => u.id === apiUser.id);
    
    if (existingIndex === -1) {
      // Thêm mới nếu chưa có
      apiUser.synced = true;
      merged.push(apiUser);
    } else {
      // Cập nhật nếu API có phiên bản mới hơn
      const localUser = merged[existingIndex];
      if (!localUser.updatedAt || 
          (apiUser.updatedAt && new Date(apiUser.updatedAt) > new Date(localUser.updatedAt))) {
        
        // Giữ lại trạng thái đồng bộ
        apiUser.synced = true;
        merged[existingIndex] = apiUser;
      }
    }
  });
  
  return merged;
}

/**
 * Thiết lập lắng nghe sự kiện localStorage
 */
function setupStorageListener() {
  window.addEventListener('storage', function(event) {
    // Bỏ qua các sự kiện không liên quan
    if (!event.key || !event.newValue) return;
    
    switch(event.key) {
      case 'products':
        console.log('Phát hiện thay đổi dữ liệu sản phẩm từ tab khác');
        dataStore.products = JSON.parse(event.newValue);
        // Thông báo sự kiện đồng bộ
        dispatchSyncEvent('products');
        break;
        
      case 'orders':
        console.log('Phát hiện thay đổi dữ liệu đơn hàng từ tab khác');
        dataStore.orders = JSON.parse(event.newValue);
        
        // Cập nhật lịch sử đơn hàng của người dùng
        const currentAccount = JSON.parse(localStorage.getItem('currentAccount'));
        if (currentAccount) {
          const userOrders = dataStore.orders.filter(order => order.userId === currentAccount.id);
          localStorage.setItem('orderHistory', JSON.stringify(userOrders));
        }
        
        // Thông báo sự kiện đồng bộ
        dispatchSyncEvent('orders');
        break;
        
      case 'users':
        if (isAdmin) {
          console.log('Phát hiện thay đổi dữ liệu người dùng từ tab khác');
          dataStore.users = JSON.parse(event.newValue);
          // Thông báo sự kiện đồng bộ
          dispatchSyncEvent('users');
        }
        break;
    }
  });
}

/**
 * Phát sự kiện đồng bộ
 */
function dispatchSyncEvent(dataType) {
  const event = new CustomEvent('nafood:sync', {
    detail: {
      type: dataType,
      timestamp: Date.now()
    }
  });
  
  window.dispatchEvent(event);
}

/**
 * Khởi tạo dữ liệu ban đầu từ API JSON nếu chưa có
 */
function initFromJsonApi() {
  // Kiểm tra xem đã có dữ liệu trong localStorage chưa
  const hasProducts = localStorage.getItem('products');
  const hasOrders = localStorage.getItem('orders');
  const hasUsers = localStorage.getItem('users');
  
  console.log('Đang khởi tạo dữ liệu ban đầu từ API JSON...');
  
  // Mảng các promise cho các request API
  const fetchPromises = [];
  
  // Tải dữ liệu sản phẩm từ API nếu chưa có
  if (!hasProducts || JSON.parse(hasProducts).length === 0) {
    const productsPromise = fetch(PRODUCTS_API)
      .then(response => {
        if (!response.ok) {
          throw new Error('Không thể tải dữ liệu sản phẩm từ API');
        }
        return response.json();
      })
      .then(products => {
        dataStore.products = products.map(product => ({
          ...product,
          synced: true
        }));
        localStorage.setItem('products', JSON.stringify(dataStore.products));
        console.log(`Đã tải ${dataStore.products.length} sản phẩm từ API`);
      })
      .catch(error => {
        console.error('Lỗi khi tải sản phẩm từ API:', error);
      });
    
    fetchPromises.push(productsPromise);
  }
  
  // Tải dữ liệu đơn hàng từ API nếu chưa có
  if (!hasOrders || JSON.parse(hasOrders).length === 0) {
    const ordersPromise = fetch(ORDERS_API)
      .then(response => {
        if (!response.ok) {
          // Có thể endpoint đơn hàng chưa tồn tại, không báo lỗi
          return [];
        }
        return response.json();
      })
      .then(orders => {
        if (orders.length > 0) {
          dataStore.orders = orders.map(order => ({
            ...order,
            synced: true
          }));
          localStorage.setItem('orders', JSON.stringify(dataStore.orders));
          console.log(`Đã tải ${dataStore.orders.length} đơn hàng từ API`);
          
          // Cập nhật lịch sử đơn hàng của người dùng
          const currentAccount = JSON.parse(localStorage.getItem('currentAccount'));
          if (currentAccount) {
            const userOrders = dataStore.orders.filter(order => order.userId === currentAccount.id);
            localStorage.setItem('orderHistory', JSON.stringify(userOrders));
          }
        }
      })
      .catch(error => {
        console.error('Lỗi khi tải đơn hàng từ API:', error);
      });
    
    fetchPromises.push(ordersPromise);
  }
  
  // Tải dữ liệu người dùng từ API nếu chưa có
  if (!hasUsers || JSON.parse(hasUsers).length === 0) {
    const usersPromise = fetch(USERS_API)
      .then(response => {
        if (!response.ok) {
          // Có thể endpoint người dùng chưa tồn tại, không báo lỗi
          return [];
        }
        return response.json();
      })
      .then(users => {
        if (users.length > 0) {
          dataStore.users = users.map(user => ({
            ...user,
            synced: true
          }));
          localStorage.setItem('users', JSON.stringify(dataStore.users));
          console.log(`Đã tải ${dataStore.users.length} người dùng từ API`);
        }
      })
      .catch(error => {
        console.error('Lỗi khi tải người dùng từ API:', error);
      });
    
    fetchPromises.push(usersPromise);
  }
  
  // Chờ tất cả các request hoàn tất
  Promise.all(fetchPromises)
    .then(() => {
      console.log('Đã khởi tạo dữ liệu ban đầu từ API JSON thành công');
    })
    .catch(error => {
      console.error('Lỗi khi khởi tạo dữ liệu từ API JSON:', error);
    });
}

/**
 * Thiết lập đồng bộ định kỳ
 */
function setupPeriodicSync() {
  // Đồng bộ mỗi 5 phút
  setInterval(function() {
    // Chỉ đồng bộ khi trình duyệt đang active
    if (!document.hidden) {
      syncWithAPI();
    }
  }, 5 * 60 * 1000);
  
  // Đồng bộ khi cửa sổ trình duyệt quay lại active
  document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
      // Kiểm tra xem lần đồng bộ cuối cùng là khi nào
      const lastSyncTime = Math.max(
        dataStore.lastSync.products,
        dataStore.lastSync.orders,
        dataStore.lastSync.users
      );
      
      // Nếu lần đồng bộ cuối cùng cách đây hơn 5 phút, thực hiện đồng bộ lại
      if (Date.now() - lastSyncTime > 5 * 60 * 1000) {
        syncWithAPI();
      }
    }
  });
}

/**
 * Thêm sản phẩm mới và đồng bộ
 */
function addProduct(product) {
  // Thêm thuộc tính đồng bộ và ID nếu chưa có
  if (!product.id) {
    product.id = generateId('product');
  }
  
  product.isNew = true;
  product.synced = false;
  product.updatedAt = new Date().toISOString();
  
  // Thêm vào cơ sở dữ liệu
  dataStore.products.push(product);
  
  // Lưu vào localStorage
  localStorage.setItem('products', JSON.stringify(dataStore.products));
  
  // Đồng bộ lên API nếu có kết nối
  syncProducts().then(() => {
    console.log('Đã thêm và đồng bộ sản phẩm:', product.id);
  });
  
  return product;
}

/**
 * Cập nhật sản phẩm
 */
function updateProduct(productId, updates) {
  // Tìm sản phẩm cần cập nhật
  const index = dataStore.products.findIndex(p => p.id === productId);
  if (index === -1) {
    console.error('Không tìm thấy sản phẩm:', productId);
    return null;
  }
  
  // Cập nhật thông tin
  const updatedProduct = { ...dataStore.products[index], ...updates };
  updatedProduct.synced = false;
  updatedProduct.updatedAt = new Date().toISOString();
  
  // Lưu vào cơ sở dữ liệu
  dataStore.products[index] = updatedProduct;
  
  // Lưu vào localStorage
  localStorage.setItem('products', JSON.stringify(dataStore.products));
  
  // Đồng bộ lên API nếu có kết nối
  syncProducts().then(() => {
    console.log('Đã cập nhật và đồng bộ sản phẩm:', productId);
  });
  
  return updatedProduct;
}

/**
 * Thêm đơn hàng mới và đồng bộ
 */
function addOrder(order) {
  // Thêm thuộc tính đồng bộ và ID nếu chưa có
  if (!order.id) {
    order.id = `NAF${Date.now()}`;
  }
  
  order.synced = false;
  order.updatedAt = new Date().toISOString();
  
  // Thêm vào cơ sở dữ liệu
  dataStore.orders.push(order);
  
  // Lưu vào localStorage
  localStorage.setItem('orders', JSON.stringify(dataStore.orders));
  
  // Cập nhật lịch sử đơn hàng của người dùng
  const currentAccount = JSON.parse(localStorage.getItem('currentAccount'));
  if (currentAccount && order.userId === currentAccount.id) {
    const userOrders = dataStore.orders.filter(o => o.userId === currentAccount.id);
    localStorage.setItem('orderHistory', JSON.stringify(userOrders));
  }
  
  // Đồng bộ lên API nếu có kết nối
  syncOrders().then(() => {
    console.log('Đã thêm và đồng bộ đơn hàng:', order.id);
  });
  
  return order;
}

/**
 * Cập nhật đơn hàng
 */
function updateOrder(orderId, updates) {
  // Tìm đơn hàng cần cập nhật
  const index = dataStore.orders.findIndex(o => o.id === orderId);
  if (index === -1) {
    console.error('Không tìm thấy đơn hàng:', orderId);
    return null;
  }
  
  // Cập nhật thông tin
  const updatedOrder = { ...dataStore.orders[index], ...updates };
  updatedOrder.synced = false;
  updatedOrder.updatedAt = new Date().toISOString();
  
  // Lưu vào cơ sở dữ liệu
  dataStore.orders[index] = updatedOrder;
  
  // Lưu vào localStorage
  localStorage.setItem('orders', JSON.stringify(dataStore.orders));
  
  // Cập nhật lịch sử đơn hàng của người dùng
  const currentAccount = JSON.parse(localStorage.getItem('currentAccount'));
  if (currentAccount && updatedOrder.userId === currentAccount.id) {
    const userOrders = dataStore.orders.filter(o => o.userId === currentAccount.id);
    localStorage.setItem('orderHistory', JSON.stringify(userOrders));
  }
  
  // Đồng bộ lên API nếu có kết nối
  syncOrders().then(() => {
    console.log('Đã cập nhật và đồng bộ đơn hàng:', orderId);
  });
  
  return updatedOrder;
}

/**
 * Thêm người dùng mới và đồng bộ
 */
function addUser(user) {
  // Chỉ admin mới có quyền
  if (!isAdmin && user.role === 'admin') {
    console.error('Không có quyền tạo tài khoản admin');
    return null;
  }
  
  // Thêm thuộc tính đồng bộ và ID nếu chưa có
  if (!user.id) {
    user.id = generateId('user');
  }
  
  user.isNew = true;
  user.synced = false;
  user.updatedAt = new Date().toISOString();
  
  // Thêm vào cơ sở dữ liệu
  dataStore.users.push(user);
  
  // Lưu vào localStorage
  localStorage.setItem('users', JSON.stringify(dataStore.users));
  
  // Đồng bộ lên API nếu có kết nối và là admin
  if (isAdmin) {
    syncUsers().then(() => {
      console.log('Đã thêm và đồng bộ người dùng:', user.id);
    });
  }
  
  return user;
}

/**
 * Cập nhật người dùng
 */
function updateUser(userId, updates) {
  // Tìm người dùng cần cập nhật
  const index = dataStore.users.findIndex(u => u.id === userId);
  if (index === -1) {
    console.error('Không tìm thấy người dùng:', userId);
    return null;
  }
  
  // Kiểm tra quyền: Chỉ admin mới có quyền hoặc người dùng tự cập nhật thông tin của mình
  const currentAccount = JSON.parse(localStorage.getItem('currentAccount'));
  if (!isAdmin && (!currentAccount || currentAccount.id !== userId)) {
    console.error('Không có quyền cập nhật thông tin người dùng');
    return null;
  }
  
  // Không cho phép thay đổi role trừ khi là admin
  if (updates.role && !isAdmin) {
    delete updates.role;
  }
  
  // Cập nhật thông tin
  const updatedUser = { ...dataStore.users[index], ...updates };
  updatedUser.synced = false;
  updatedUser.updatedAt = new Date().toISOString();
  
  // Lưu vào cơ sở dữ liệu
  dataStore.users[index] = updatedUser;
  
  // Lưu vào localStorage
  localStorage.setItem('users', JSON.stringify(dataStore.users));
  
  // Đồng bộ lên API nếu có kết nối và là admin
  if (isAdmin) {
    syncUsers().then(() => {
      console.log('Đã cập nhật và đồng bộ người dùng:', userId);
    });
  }
  
  return updatedUser;
}

/**
 * Lấy danh sách sản phẩm
 */
function getProducts(filters = {}) {
  let filtered = [...dataStore.products];
  
  // Lọc theo danh mục
  if (filters.category) {
    filtered = filtered.filter(product => 
      product.category === filters.category
    );
  }
  
  // Lọc theo từ khóa
  if (filters.keyword) {
    const keyword = filters.keyword.toLowerCase();
    filtered = filtered.filter(product => 
      product.title.toLowerCase().includes(keyword) || 
      (product.desc && product.desc.toLowerCase().includes(keyword))
    );
  }
  
  // Lọc theo trạng thái (chỉ hiển thị sản phẩm active)
  if (filters.activeOnly) {
    filtered = filtered.filter(product => product.status === 1);
  }
  
  // Sắp xếp
  if (filters.sortBy) {
    switch(filters.sortBy) {
      case 'price_asc':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'price_desc':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'name_asc':
        filtered.sort((a, b) => a.title.localeCompare(b.title));
        break;
      case 'name_desc':
        filtered.sort((a, b) => b.title.localeCompare(a.title));
        break;
    }
  }
  
  return filtered;
}

/**
 * Lấy danh sách đơn hàng
 */
function getOrders(filters = {}) {
  let filtered = [...dataStore.orders];
  
  // Lọc theo người dùng
  if (filters.userId) {
    filtered = filtered.filter(order => 
      order.userId === filters.userId
    );
  }
  
  // Lọc theo trạng thái
  if (filters.status) {
    filtered = filtered.filter(order => 
      order.orderStatus === filters.status
    );
  }
  
  // Lọc theo từ khóa
  if (filters.keyword) {
    const keyword = filters.keyword.toLowerCase();
    filtered = filtered.filter(order => 
      order.id.toLowerCase().includes(keyword) || 
      order.customerName.toLowerCase().includes(keyword) ||
      (order.phone && order.phone.includes(keyword))
    );
  }
  
  // Lọc theo phương thức thanh toán
  if (filters.paymentMethod) {
    filtered = filtered.filter(order => 
      order.paymentMethod === filters.paymentMethod
    );
  }
  
  // Sắp xếp mặc định theo thời gian giảm dần (mới nhất lên đầu)
  filtered.sort((a, b) => new Date(b.orderDate) - new Date(a.orderDate));
  
  return filtered;
}

/**
 * Lấy danh sách người dùng
 */
function getUsers(filters = {}) {
  // Chỉ admin mới có quyền xem tất cả người dùng
  if (!isAdmin) {
    const currentAccount = JSON.parse(localStorage.getItem('currentAccount'));
    if (currentAccount) {
      return [dataStore.users.find(u => u.id === currentAccount.id)].filter(Boolean);
    }
    return [];
  }
  
  let filtered = [...dataStore.users];
  
  // Lọc theo vai trò
  if (filters.role) {
    filtered = filtered.filter(user => 
      user.role === filters.role
    );
  }
  
  // Lọc theo trạng thái
  if (filters.status) {
    filtered = filtered.filter(user => 
      user.status === filters.status
    );
  }
  
  // Lọc theo từ khóa
  if (filters.keyword) {
    const keyword = filters.keyword.toLowerCase();
    filtered = filtered.filter(user => 
      user.username.toLowerCase().includes(keyword) || 
      user.email.toLowerCase().includes(keyword) ||
      (user.fullName && user.fullName.toLowerCase().includes(keyword)) ||
      (user.phone && user.phone.includes(keyword))
    );
  }
  
  // Sắp xếp mặc định theo tên
  filtered.sort((a, b) => a.username.localeCompare(b.username));
  
  return filtered;
}

/**
 * Tạo ID duy nhất
 */
function generateId(prefix) {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 10000);
  return `${prefix}_${timestamp}_${random}`;
}

// Export các hàm để sử dụng từ các module khác
window.nafoodSync = {
  getProducts,
  getOrders,
  getUsers,
  addProduct,
  updateProduct,
  addOrder,
  updateOrder,
  addUser,
  updateUser,
  syncWithAPI
};