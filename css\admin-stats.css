/* CSS cho các phần thống kê trong trang Admin */

/* Thống kê tổng quan */
.overview-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

@media (max-width: 1200px) {
  .overview-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .overview-stats {
    grid-template-columns: 1fr;
  }
}

.stat-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  align-items: center;
  transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stat-card.revenue .stat-icon {
  background-color: #4e73df;
}

.stat-card.orders .stat-icon {
  background-color: #1cc88a;
}

.stat-card.products .stat-icon {
  background-color: #f6c23e;
}

.stat-card.users .stat-icon {
  background-color: #e74a3b;
}

.stat-content {
  flex-grow: 1;
}

.stat-content h3 {
  margin: 0 0 5px;
  font-size: 14px;
  color: #5a5c69;
  font-weight: 600;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #111;
  margin-bottom: 5px;
}

.stat-subtitle {
  font-size: 12px;
  color: #666;
}

/* Biểu đồ thống kê */
.stats-section {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

@media (max-width: 992px) {
  .stats-section {
    grid-template-columns: 1fr;
  }
}

.stats-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stats-card h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  color: #333;
  font-weight: 600;
}

.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

/* Đơn hàng gần đây */
.stats-card.full-width {
  grid-column: 1 / -1;
}

.recent-orders-table {
  width: 100%;
  overflow-x: auto;
}

.recent-orders-table table {
  width: 100%;
  border-collapse: collapse;
}

.recent-orders-table th,
.recent-orders-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.recent-orders-table th {
  background-color: #f8f9fa;
  color: #333;
  font-weight: 600;
}

.recent-orders-table tbody tr:hover {
  background-color: #f5f5f5;
}

.text-center {
  text-align: center;
}

/* Styles cho trạng thái đơn hàng */
.order-status {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.status-processing {
  background-color: #fff3e0;
  color: #e65100;
}

.status-confirmed {
  background-color: #e3f2fd;
  color: #0d47a1;
}

.status-shipping {
  background-color: #f3e5f5;
  color: #6a1b9a;
}

.status-delivered {
  background-color: #e8f5e9;
  color: #1b5e20;
}

.status-cancelled {
  background-color: #ffebee;
  color: #b71c1c;
}

/* Nút làm mới thống kê */
#refresh-stats-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 16px;
  background-color: #4e73df;
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

#refresh-stats-btn:hover {
  background-color: #2e59d9;
}

#refresh-stats-btn i {
  font-size: 14px;
}

#refresh-stats-btn.loading i {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}