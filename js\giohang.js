document.addEventListener("DOMContentLoaded", function () {
  // L<PERSON>y các phần tử cần thiết
  const cartBtn = document.querySelector('.cart-btn'); // Nút mở giỏ hàng
  const cartContainer = document.querySelector('.cart-container'); // Giỏ hàng
  const closeBtn = document.querySelector('.close-btn'); // Nút đóng giỏ hàng
  const overlay = document.createElement('div'); // Tạo lớp phủ (overlay)
  
  // Thêm lớp phủ vào DOM
  overlay.classList.add('overlay');
  document.body.appendChild(overlay);
  
  // Khởi tạo giỏ hàng và sản phẩm
  let cartItems = [];
  let cartCount = 0;
  updateCartDisplay();
  
  // Kiểm tra xem các phần tử cần thiết có tồn tại không
  if (cartBtn && cartContainer && closeBtn) {
    // Mở giỏ hàng và hiển thị overlay
    cartBtn.addEventListener('click', () => {
      cartContainer.classList.add('active');
      overlay.classList.add('active'); // Hiển thị lớp phủ
      document.body.style.overflow = 'hidden'; // Ngăn cuộn trang
    });
    
    // Đóng giỏ hàng và ẩn overlay
    closeBtn.addEventListener('click', closeCart);
    
    // Đóng giỏ hàng khi nhấn vào overlay
    overlay.addEventListener('click', closeCart);
  }
  
  // Hàm đóng giỏ hàng
  function closeCart() {
    cartContainer.classList.remove('active');
    overlay.classList.remove('active'); // Ẩn lớp phủ
    document.body.style.overflow = ''; // Cho phép cuộn trang trở lại
  }
  
  // Thêm sản phẩm vào giỏ hàng
  function addToCart(event) {
    const button = event.currentTarget;
    const productId = button.getAttribute('data-id');
    const productName = button.getAttribute('data-name');
    const productPrice = parseFloat(button.getAttribute('data-price'));
    const productImage = button.getAttribute('data-image');
    
    // Kiểm tra sản phẩm đã có trong giỏ hàng chưa
    const existingItem = cartItems.find(item => item.id === productId);
    
    if (existingItem) {
      // Nếu sản phẩm đã có trong giỏ, tăng số lượng
      existingItem.quantity += 1;
    } else {
      // Nếu sản phẩm chưa có trong giỏ, thêm mới
      cartItems.push({
        id: productId,
        name: productName,
        price: productPrice,
        image: productImage,
        quantity: 1
      });
    }
    
    // Cập nhật số lượng và hiển thị giỏ hàng
    cartCount++;
    updateCartDisplay();
    
    // Hiệu ứng thông báo đã thêm vào giỏ hàng
    const toast = document.createElement('div');
    toast.classList.add('toast');
    toast.textContent = `Đã thêm ${productName} vào giỏ hàng!`;
    document.body.appendChild(toast);
    
    setTimeout(() => {
      toast.classList.add('show');
      setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
          document.body.removeChild(toast);
        }, 300);
      }, 2000);
    }, 100);
  }
  
  // Cập nhật hiển thị giỏ hàng
  function updateCartDisplay() {
    // Cập nhật số lượng sản phẩm hiển thị trên nút giỏ hàng
    const cartCountElement = document.getElementById('cart-count');
    if (cartCountElement) {
      cartCountElement.textContent = cartCount;
      
      // Hiển thị hoặc ẩn số lượng
      if (cartCount > 0) {
        cartCountElement.style.display = 'block';
      } else {
        cartCountElement.style.display = 'none';
      }
    }
    
    // Cập nhật nội dung giỏ hàng
    const cartItemsContainer = document.querySelector('.cart-items');
    if (!cartItemsContainer) return;
    
    // Xóa nội dung hiện tại
    cartItemsContainer.innerHTML = '';
    
    // Kiểm tra giỏ hàng có trống không
    if (cartItems.length === 0) {
      // Hiển thị trạng thái giỏ hàng trống
      const emptyCart = document.createElement('div');
      emptyCart.classList.add('cart-empty');
      emptyCart.innerHTML = `
        <div class="cart-empty-icon"></div>
        <h3 class="empty-title">Giỏ hàng trống</h3>
        <p>Không có sản phẩm nào trong giỏ hàng của bạn</p>
      `;
      cartItemsContainer.appendChild(emptyCart);
    } else {
      // Thêm từng sản phẩm vào giỏ hàng
      cartItems.forEach(item => {
        const cartItem = document.createElement('div');
        cartItem.classList.add('cart-item');
        cartItem.innerHTML = `
          <img src="${item.image}" alt="${item.name}" class="cart-item-image">
          <div class="cart-item-details">
            <h4 class="cart-item-title">${item.name}</h4>
            <div class="cart-item-price">${formatPrice(item.price * item.quantity)}</div>
            <div class="cart-item-quantity">
              <button class="quantity-btn decrease" data-id="${item.id}">-</button>
              <span class="quantity-value">${item.quantity}</span>
              <button class="quantity-btn increase" data-id="${item.id}">+</button>
            </div>
          </div>
          <button class="cart-item-remove" data-id="${item.id}">&times;</button>
        `;
        cartItemsContainer.appendChild(cartItem);
      });
      
      // Thêm sự kiện tăng/giảm/xóa sản phẩm
      cartItemsContainer.querySelectorAll('.decrease').forEach(btn => {
        btn.addEventListener('click', decreaseQuantity);
      });
      
      cartItemsContainer.querySelectorAll('.increase').forEach(btn => {
        btn.addEventListener('click', increaseQuantity);
      });
      
      cartItemsContainer.querySelectorAll('.cart-item-remove').forEach(btn => {
        btn.addEventListener('click', removeFromCart);
      });
    }
    
    // Cập nhật tổng tiền
    updateCartTotal();
  }
  
  // Giảm số lượng sản phẩm
  function decreaseQuantity(event) {
    const productId = event.currentTarget.getAttribute('data-id');
    const item = cartItems.find(item => item.id === productId);
    
    if (item && item.quantity > 1) {
      item.quantity -= 1;
      cartCount--;
    } else if (item && item.quantity === 1) {
      // Nếu số lượng là 1, xóa sản phẩm khỏi giỏ hàng
      cartItems = cartItems.filter(item => item.id !== productId);
      cartCount--;
    }
    
    updateCartDisplay();
  }
  
  // Tăng số lượng sản phẩm
  function increaseQuantity(event) {
    const productId = event.currentTarget.getAttribute('data-id');
    const item = cartItems.find(item => item.id === productId);
    
    if (item) {
      item.quantity += 1;
      cartCount++;
      updateCartDisplay();
    }
  }
  
  // Xóa sản phẩm khỏi giỏ hàng
  function removeFromCart(event) {
    const productId = event.currentTarget.getAttribute('data-id');
    const item = cartItems.find(item => item.id === productId);
    
    if (item) {
      cartCount -= item.quantity;
      cartItems = cartItems.filter(item => item.id !== productId);
      updateCartDisplay();
    }
  }
  
  // Cập nhật tổng tiền giỏ hàng
  function updateCartTotal() {
    const subtotalElement = document.querySelector('.subtotal-value');
    const totalElement = document.querySelector('.total-value');
    
    if (!subtotalElement || !totalElement) return;
    
    const subtotal = cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
    const shipping = subtotal > 0 ? 30000 : 0; // Phí ship 30.000đ nếu có sản phẩm
    const total = subtotal + shipping;
    
    subtotalElement.textContent = formatPrice(subtotal);
    totalElement.textContent = formatPrice(total);
    
    // Cập nhật phí ship
    const shippingElement = document.querySelector('.shipping-value');
    if (shippingElement) {
      if (subtotal > 0) {
        if (subtotal >= 300000) { // Miễn phí ship cho đơn từ 300.000đ
          shippingElement.textContent = 'Miễn phí';
          shippingElement.classList.add('free');
        } else {
          shippingElement.textContent = formatPrice(shipping);
          shippingElement.classList.remove('free');
        }
      } else {
        shippingElement.textContent = 'Chưa áp dụng';
      }
    }
  }
  
  // Định dạng giá tiền
  function formatPrice(price) {
    return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(price);
  }
  
  // Gắn sự kiện cho các nút "Thêm vào giỏ hàng"
  document.querySelectorAll('.add-to-cart-btn').forEach(btn => {
    btn.addEventListener('click', addToCart);
  });
});