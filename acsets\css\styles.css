/* Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  background-color: #f9f9f9;
  color: #333;
  overflow-x: hidden; /* Prevent horizontal scroll */
}

.nafood-container {
  max-width: 1300px;
  width: 100%;
  margin: 0 auto;
  padding: 0 20px;
  background-color: #fff;
  box-shadow: 0 0 10px rgba(0,0,0,0.05);
}

.container {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  padding: 0 16px;
}

/* Header */
/* Header */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  position: sticky;     /* Thay vì fixed */
  top: 0;
  z-index: 1000;
  width: 100%;
  flex-wrap: wrap;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Navigation */
.nav {
  display: flex;
  justify-content: space-around;
  align-items: center;
  background: white;
  border-bottom: 5px solid #ddd;
  padding: 16px 0;
  font-weight: bold;
  width: 100%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  position: sticky;     /* Thay vì fixed */
  top: 70px;            /* Khoảng cách bên dưới header */
  z-index: 999;
  transition: padding 0.4s ease, transform 0.3s ease, background-color 0.3s ease;
}


/* Logo */
.logo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo-img {
  height: 50px;
  width: 50px;
  object-fit: contain;
}

.logo-text {
  font-family: 'Lobster', cursive;
  font-size: 28px;
  line-height: 1.6;
  font-weight: normal;
  color: #b71c1c;
}

/* Form tìm kiếm */
.form-search {
  display: flex;
  align-items: center;
  background-color: #f1f1f1;
  padding: 6px 10px;
  border-radius: 30px;
  width: 100%;
  max-width: 500px;
  margin: 0 20px;
  position: relative;
}

.form-search-input {
  border: none;
  outline: none;
  background: transparent;
  padding: 8px 10px;
  flex: 1;
  font-size: 16px;
  width: 100%;
}

.search-icon {
  color: #555;
  margin-left: 8px;
}

.filter-btn {
  background-color: #b71c1c;
  color: white;
  border: none;
  padding: 6px 16px;
  border-radius: 20px;
  margin-left: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
}

/* Icon bên phải */
.header-middle-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-middle-right-item {
  list-style: none;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #b71c1c;
  cursor: pointer;
}

.auth-container .text-dndk {
  font-size: 12px;
  line-height: 1.2;
  display: block;
  color: black;
}

.auth-container .text-tk {
  font-size: 12px;
  line-height: 1.2;
  display: block;
  font-weight: bold;
  color: black;
}

.cart-btn {
  background: none;
  border: none;
  color: #b71c1c;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  font-weight: 500;
  position: relative;
}

.cart-btn i {
  position: relative;
}

.cart-count {
  position: absolute;
  top: -6px;
  right: 65px;
  background-color: red;
  color: white;
  border-radius: 50%;
  padding: 1px 4px;
  font-size: 7px;
  font-weight: bold;
  line-height: 1;
  min-width: 10px;
  height: 10px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

/* Navigation */
.nav {
  display: flex;
  justify-content: space-around;
  align-items: center;
  background: white;
  border-bottom: 5px solid #ddd;
  padding: 16px 0;
  font-weight: bold;
  width: 100%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  position: sticky;
  top: 70px;
  z-index: 999;
  transition:
    transform 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    padding 0.4s ease,
    background-color 0.4s ease;
  will-change: transform, padding;
}

.nav.shrink {
  padding: 8px 0;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.nav.hide {
  transform: translateY(-100%);
}



    .nav-link {
      text-decoration: none;
      color: black;
      padding: 10px 15px;
      font-size: 16px;
      position: relative;
      transition: color 0.3s ease-in-out;
    }

    .nav-link:hover {
      color: #b22222;
    }

    .nav-link::after {
      content: "";
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 2px;
      background-color: #b22222;
      transform: scaleX(0);
      transition: transform 0.3s ease-in-out;
      transform-origin: right;
    }

    .nav-link:hover::after {
      transform: scaleX(1);
      transform-origin: left;
    }

    .content {
      height: 2000px;
      background: linear-gradient(#f5f5f5, #ddd);
    }

/* Banner */
.banner {
  background-image: url('../image/banner.png');
  background-size: cover;
  background-position: center;
  height: 600px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: white;
  margin-top: 20px;
  border-radius: 10px;
  width: 100%;
}

.banner-content {
  background-color: rgba(187, 177, 177, 0.5);
  padding: 40px;
  border-radius: 8px;
  width: 80%;
  max-width: 800px;
}

.banner-title {
  font-family: 'Lobster', cursive;
  font-size: 48px;
  margin-bottom: 10px;
}

.banner-subtitle {
  font-size: 24px;
  margin-bottom: 20px;
}

.order-btn {
  background-color: #b71c1c;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.order-btn:hover {
  background-color: #9b1a1a;
}

/* Services */
.services {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin: 40px 0;
  gap: 20px;
}

.service-item {
  background: white;
  flex: 1;
  min-width: 250px;
  display: flex;
  align-items: center;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
}

.service-item i {
  font-size: 28px;
  color: #c0392b;
  margin-right: 15px;
}

.service-title {
  font-size: 16px;
  margin-bottom: 5px;
}

/* Menu Section */
.menu-heading {
  text-align: center;
  margin: 50px 0 20px;
}

.menu-title {
  font-size: 28px;
  margin-bottom: 10px;
  color: #c0392b;
}

.underline {
  width: 80px;
  height: 3px;
  background: #c0392b;
  margin: 0 auto;
}

/* Footer */
footer {
  background: #fff;
  border-top: 1px solid #ddd;
  margin-top: 40px;
  width: 100%;
}

.footer-container {
  padding: 40px 20px;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.footer-links {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
}

.footer-menu ul,
.footer-about p,
.footer-contact p {
  font-size: 14px;
}

.footer-menu ul li,
.footer-about,
.footer-contact {
  list-style: none;
  margin-bottom: 8px;
}

.footer-logo img {
  width: 40px;
}

.footer-bottom {
  text-align: center;
  padding: 15px 0;
  font-size: 14px;
  background: #f1f1f1;
  width: 100%;
}

/* Khung bộ lọc */
.filter-bar {
  position: fixed;
  top: 75px;
  left: 50%;
  transform: translateX(-50%);
  width: 1260px;
  max-width: 95%;
  height: auto;
  min-height: 60px;
  background-color: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 10px 20px;
  font-family: 'Poppins', sans-serif;
  border-radius: 0px;
  flex-wrap: wrap;
}

/* Ẩn khi chưa bật */
.filter-bar.hidden {
  display: none;
}

/* Label */
.filter-label {
  font-size: 14px;
  margin-right: 4px;
  color: #333;
}

/* Select và input */
.filter-select,
.filter-input {
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 14px;
  background-color: #f5f5f5;
  min-width: 100px;
}

/* Nút chung */
.search-btn,
.sort-btn,
.refresh-btn,
.close-btn {
  padding: 8px 10px;
  background-color: #eee;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  color: #800000;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Nút tìm đặc biệt màu đỏ */
.search-btn {
  background-color: #a80505;
  color: #fff;
}

.search-btn:hover {
  background-color: #8b0303;
}

.sort-btn:hover,
.refresh-btn:hover,
.close-btn:hover {
  background-color: #ddd;
}

button i {
  pointer-events: none;
}

/* Mobile Menu Toggle Button (hidden by default) */
.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 24px;
  color: #b71c1c;
  cursor: pointer;
}

/* Responsive Media Queries */
/* For Tablets */
@media screen and (max-width: 992px) {
  .header {
      padding: 10px;
  }
  
  .form-search {
      max-width: 300px;
      margin: 0 10px;
  }
  
  .logo-text {
      font-size: 24px;
  }
  
  .banner {
      height: 450px;
  }
  
  .banner-title {
      font-size: 36px;
  }
  
  .banner-subtitle {
      font-size: 20px;
  }
  
  .service-item {
      min-width: 200px;
  }
  
  .filter-bar {
      top: 70px;
      padding: 8px 15px;
  }
}

/* For Mobile Phones */
@media screen and (max-width: 768px) {
  .header {
      flex-direction: column;
      padding: 10px 5px;
  }
  
  .form-search {
      margin: 10px 0;
      max-width: 100%;
      order: 3;
  }
  
  .header-middle-right {
      margin-top: 10px;
      justify-content: center;
      width: 100%;
      order: 2;
  }
  
  .logo {
      margin-bottom: 5px;
      justify-content: center;
      width: 100%;
      order: 1;
  }
  
  .nav {
      flex-direction: column;
      padding: 5px 0;
  }
  
  .nav-link {
      padding: 8px 10px;
      width: 100%;
      text-align: center;
  }
  
  .banner {
      height: 350px;
  }
  
  .banner-content {
      padding: 20px;
      width: 90%;
  }
  
  .banner-title {
      font-size: 28px;
  }
  
  .banner-subtitle {
      font-size: 16px;
  }
  
  .service-item {
      min-width: 100%;
  }
  
  .filter-bar {
      top: auto;
      bottom: 0;
      width: 100%;
      border-radius: 0;
  }
  
  .filter-select,
  .filter-input {
      min-width: 80px;
  }
  
  .search-btn,
  .sort-btn,
  .refresh-btn,
  .close-btn {
      padding: 6px 8px;
      font-size: 14px;
  }
  
  .mobile-menu-toggle {
      display: block;
      position: absolute;
      top: 15px;
      right: 15px;
  }
  
  .footer-links {
      flex-direction: column;
  }
  
  .cart-count {
      right: auto;
      top: -5px;
      left: 15px;
  }
}

/* For Small Mobile Phones */
@media screen and (max-width: 480px) {
  .logo-text {
      font-size: 20px;
  }
  
  .logo-img {
      height: 40px;
      width: 40px;
  }
  
  .banner {
      height: 300px;
      margin-top: 10px;
  }
  
  .banner-title {
      font-size: 24px;
  }
  
  .banner-subtitle {
      font-size: 14px;
      margin-bottom: 15px;
  }
  
  .order-btn {
      padding: 8px 15px;
      font-size: 14px;
  }
  
  .menu-title {
      font-size: 24px;
  }
  
  .header-middle-right {
      gap: 10px;
  }
  
  .filter-bar {
      gap: 8px;
      padding: 8px;
  }
  
  .filter-select,
  .filter-input,
  .search-btn,
  .sort-btn,
  .refresh-btn,
  .close-btn {
      font-size: 12px;
      padding: 6px;
  }
}

/* For better handling of navigation items on medium screens */
@media screen and (max-width: 1100px) and (min-width: 769px) {
  .nav {
      justify-content: center;
      flex-wrap: wrap;
  }
  
  .nav-link {
      padding: 8px 10px;
      font-size: 14px;
  }
}

/* For handling the cart count position across different screen sizes */
@media screen and (max-width: 992px) {
  .cart-count {
      right: 60px;
  }
}

@media screen and (max-width: 768px) {
  .cart-btn {
      justify-content: center;
  }
}

/* For better handling of filter bar on mid-sized screens */
@media screen and (max-width: 992px) and (min-width: 769px) {
  .filter-bar {
      width: 95%;
  }
}

/* For landscape orientation on mobile devices */
@media screen and (max-height: 500px) and (orientation: landscape) {
  .banner {
      height: 250px;
  }
  
  .filter-bar {
      position: static;
      transform: none;
      left: auto;
      margin: 10px auto;
  }
}

/* For high-resolution displays */
@media screen and (min-width: 1400px) {
  .nafood-container,
  .container {
      max-width: 1400px;
  }
  
  .banner {
      height: 700px;
  }
}

  .product-card {
    border: 1px solid #ccc;
    padding: 12px;
    margin: 10px;
    width: 250px;
    display: inline-block;
    vertical-align: top;
    border-radius: 8px;
  }

  .product-card img {
    width: 100%;
    height: 160px;
    object-fit: cover;
  }

  .page-nav-list {
    list-style: none;
    padding: 0;
    display: flex;
    gap: 8px;
    justify-content: center;
    margin-top: 20px;
  }

  .page-nav-list li {
    padding: 6px 12px;
    background-color: #eee;
    cursor: pointer;
    border-radius: 4px;
  }

  .page-nav-list li.active {
    background-color: #007BFF;
    color: white;
  }

/*SANPHAM*/

  .product-card {
    border: 1px solid #ccc;
    padding: 12px;
    margin: 10px;
    width: 250px;
    display: inline-block;
    vertical-align: top;
    border-radius: 8px;
  }

  .product-card img {
    width: 100%;
    height: 160px;
    object-fit: cover;
  }

  .page-nav-list {
    list-style: none;
    padding: 0;
    display: flex;
    gap: 8px;
    justify-content: center;
    margin-top: 20px;
  }

  .page-nav-list li {
    padding: 6px 12px;
    background-color: #eee;
    cursor: pointer;
    border-radius: 4px;
  }

  .page-nav-list li.active {
    background-color: #007BFF;
    color: white;
  }

/*SANPHAM*/
/* Nền tối mờ khi mở modal */
.modal {
  position: fixed;
  z-index: 999;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Khung modal */
.modal-content {
  background-color: #fff;
  padding: 24px;
  border-radius: 16px;
  max-width: 600px;
  width: 90%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: fadeIn 0.3s ease;
}

/* Hiệu ứng xuất hiện */
@keyframes fadeIn {
  from {opacity: 0; transform: scale(0.95);}
  to {opacity: 1; transform: scale(1);}
}

/* Nút đóng */
.close {
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
  float: right;
}

/* Ảnh món ăn */
#modalImage {
  width: 100%;
  border-radius: 12px;
  margin-bottom: 16px;
}

/* Tên món và giá */
#modalName {
  font-size: 22px;
  margin-bottom: 6px;
}
#modalPrice {
  font-size: 18px;
  color: #c62828;
  font-weight: bold;
}

/* Mô tả món ăn */
#modalDesc {
  margin: 12px 0;
  line-height: 1.5;
  color: #444;
}

/* Nút tăng giảm số lượng */
#decrease,
#increase {
  background-color: #eee;
  border: none;
  padding: 6px 12px;
  margin: 0 8px;
  font-size: 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s;
}
#decrease:hover,
#increase:hover {
  background-color: #ddd;
}

/* Text ghi chú */
#note {
  width: 100%;
  padding: 10px;
  border-radius: 10px;
  border: 1px solid #ccc;
  resize: vertical;
  margin-top: 12px;
  margin-bottom: 16px;
  font-size: 14px;
}

/* Tổng tiền */
#modalTotal {
  font-size: 18px;
  color: #c62828;
  font-weight: bold;
}

/* Nhóm nút hành động */
.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
}

/* Nút đặt hàng & giỏ hàng */
#orderNow,
#cartIcon {
  background-color: #c62828;
  color: white;
  border: none;
  padding: 10px 18px;
  border-radius: 24px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s, transform 0.2s;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

/* Hover nút */
#orderNow:hover,
#cartIcon:hover {
  background-color: #b71c1c;
  transform: scale(1.05);
}

/* Icon giỏ hàng */
#cartIcon i {
  font-size: 16px;
}
/* Vùng hiển thị sản phẩm 4 cột, 3 dòng */
.home-products {
  display: grid;
  grid-template-columns: repeat(4, 1fr); /* 4 sản phẩm mỗi dòng */
  gap: 24px;
  padding: 24px;
}

/* Thẻ sản phẩm */
.product-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  text-align: center;
}

/* Hover */
.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 20px rgba(0,0,0,0.15);
}

/* Ảnh sản phẩm */
.product-card img {
  width: 100%;
  height: 160px;
  object-fit: cover;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
}

/* Tên sản phẩm */
.product-card h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 12px 8px 4px;
  color: #333;
}

/* Giá sản phẩm */
.product-card p {
  font-size: 14px;
  color: #d32f2f;
  font-weight: bold;
  margin: 0 0 12px;
}

/* Nút đặt món */
.product-card button {
  background-color: #d32f2f;
  color: white;
  border: none;
  border-radius: 24px;
  padding: 10px 16px;
  margin: 0 16px 16px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s, transform 0.2s;
}

.product-card button:hover {
  background-color: #b71c1c;
  transform: scale(1.05);
}

/* Phân trang */
.page-nav-list {
  display: flex;
  justify-content: center;
  gap: 10px;
  list-style: none;
  padding: 20px 0;
}

.page-nav-list li {
  width: 35px;
  height: 35px;
  border: 1px solid #be1e2d;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #be1e2d;
  font-weight: bold;
  transition: background-color 0.3s, color 0.3s;
}

.page-nav-list li.active,
.page-nav-list li:hover {
  background-color: #be1e2d;
  color: white;
}
